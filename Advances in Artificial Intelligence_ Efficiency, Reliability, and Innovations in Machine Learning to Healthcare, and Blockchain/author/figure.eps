%!PS-Adobe-2.0 EPSF-2.0
%%BoundingBox: 180 324 319 519
%%Creator: CorelDRAW!
%%Title: 03-19H.EPS
%%CreationDate: Thu Jun 19 08:12:00 1997
%%DocumentProcessColors: Black
%%DocumentSuppliedResources: (atend)
%%DocumentNeededResources: (atend)
%%EndComments
%%BeginSetup
/AutoFlatness true def
%Color profile: Deaktiviert
%%EndSetup
%%BeginProlog
%%BeginResource: procset wCorel5Dict
% Copyright (c)1992-94 Corel Corporation
% All rights reserved.     v5.0 r1.6
/wCorel5Dict 300 dict def wCorel5Dict begin/bd{bind def}bind def/ld{load def}
bd/xd{exch def}bd/_ null def/rp{{pop}repeat}bd/@cp/closepath ld/@gs/gsave ld
/@gr/grestore ld/@np/newpath ld/Tl/translate ld/$sv 0 def/@sv{/$sv save def}bd
/@rs{$sv restore}bd/spg/showpage ld/showpage{}bd currentscreen/@dsp xd/$dsp
/@dsp def/$dsa xd/$dsf xd/$sdf false def/$SDF false def/$Scra 0 def/SetScr
/setscreen ld/setscreen{3 rp}bd/@ss{2 index 0 eq{$dsf 3 1 roll 4 -1 roll pop}
if exch $Scra add exch load SetScr}bd/SepMode_5 where{pop}{/SepMode_5 0 def}
ifelse/CurrentInkName_5 where{pop}{/CurrentInkName_5(Composite)def}ifelse
/$ink_5 where{pop}{/$ink_5 -1 def}ifelse/$c 0 def/$m 0 def/$y 0 def/$k 0 def
/$t 1 def/$n _ def/$o 0 def/$fil 0 def/$C 0 def/$M 0 def/$Y 0 def/$K 0 def/$T 1
def/$N _ def/$O 0 def/$PF false def/s1c 0 def/s1m 0 def/s1y 0 def/s1k 0 def
/s1t 0 def/s1n _ def/$bkg false def/SK 0 def/SM 0 def/SY 0 def/SC 0 def/$op
false def matrix currentmatrix/$ctm xd/$ptm matrix def/$ttm matrix def/$stm
matrix def/$fst 128 def/$pad 0 def/$rox 0 def/$roy 0 def/$ffpnt true def
/CorelDrawReencodeVect[16#0/grave 16#5/breve 16#6/dotaccent 16#8/ring
16#A/hungarumlaut 16#B/ogonek 16#C/caron 16#D/dotlessi 16#27/quotesingle
16#60/grave 16#7C/bar
16#82/quotesinglbase/florin/quotedblbase/ellipsis/dagger/daggerdbl
16#88/circumflex/perthousand/Scaron/guilsinglleft/OE
16#91/quoteleft/quoteright/quotedblleft/quotedblright/bullet/endash/emdash
16#98/tilde/trademark/scaron/guilsinglright/oe 16#9F/Ydieresis
16#A1/exclamdown/cent/sterling/currency/yen/brokenbar/section
16#a8/dieresis/copyright/ordfeminine/guillemotleft/logicalnot/minus/registered/macron
16#b0/degree/plusminus/twosuperior/threesuperior/acute/mu/paragraph/periodcentered
16#b8/cedilla/onesuperior/ordmasculine/guillemotright/onequarter/onehalf/threequarters/questiondown
16#c0/Agrave/Aacute/Acircumflex/Atilde/Adieresis/Aring/AE/Ccedilla
16#c8/Egrave/Eacute/Ecircumflex/Edieresis/Igrave/Iacute/Icircumflex/Idieresis
16#d0/Eth/Ntilde/Ograve/Oacute/Ocircumflex/Otilde/Odieresis/multiply
16#d8/Oslash/Ugrave/Uacute/Ucircumflex/Udieresis/Yacute/Thorn/germandbls
16#e0/agrave/aacute/acircumflex/atilde/adieresis/aring/ae/ccedilla
16#e8/egrave/eacute/ecircumflex/edieresis/igrave/iacute/icircumflex/idieresis
16#f0/eth/ntilde/ograve/oacute/ocircumflex/otilde/odieresis/divide
16#f8/oslash/ugrave/uacute/ucircumflex/udieresis/yacute/thorn/ydieresis]def
/@BeginSysCorelDict{systemdict/Corel20Dict known{systemdict/Corel20Dict get
exec}if}bd/@EndSysCorelDict{systemdict/Corel20Dict known{end}if}bd AutoFlatness
{/@ifl{dup currentflat exch sub 10 gt{
([Error: PathTooComplex; OffendingCommand: AnyPaintingOperator]\n)print flush
@np exit}{currentflat 2 add setflat}ifelse}bd/@fill/fill ld/fill{currentflat{
{@fill}stopped{@ifl}{exit}ifelse}bind loop setflat}bd/@eofill/eofill ld/eofill
{currentflat{{@eofill}stopped{@ifl}{exit}ifelse}bind loop setflat}bd/@clip
/clip ld/clip{currentflat{{@clip}stopped{@ifl}{exit}ifelse}bind loop setflat}
bd/@eoclip/eoclip ld/eoclip{currentflat{{@eoclip}stopped{@ifl}{exit}ifelse}
bind loop setflat}bd/@stroke/stroke ld/stroke{currentflat{{@stroke}stopped
{@ifl}{exit}ifelse}bind loop setflat}bd}if/d/setdash ld/j/setlinejoin ld/J
/setlinecap ld/M/setmiterlimit ld/w/setlinewidth ld/O{/$o xd}bd/R{/$O xd}bd/W
/eoclip ld/c/curveto ld/C/c ld/l/lineto ld/L/l ld/rl/rlineto ld/m/moveto ld/n
/newpath ld/N/newpath ld/P{11 rp}bd/u{}bd/U{}bd/A{pop}bd/q/@gs ld/Q/@gr ld/`
{}bd/~{}bd/@{}bd/&{}bd/@j{@sv @np}bd/@J{@rs}bd/g{1 exch sub/$k xd/$c 0 def/$m 0
def/$y 0 def/$t 1 def/$n _ def/$fil 0 def}bd/G{1 sub neg/$K xd _ 1 0 0 0/$C xd
/$M xd/$Y xd/$T xd/$N xd}bd/k{1 index type/stringtype eq{/$t xd/$n xd}{/$t 0
def/$n _ def}ifelse/$k xd/$y xd/$m xd/$c xd/$fil 0 def}bd/K{1 index type
/stringtype eq{/$T xd/$N xd}{/$T 0 def/$N _ def}ifelse/$K xd/$Y xd/$M xd/$C xd
}bd/x/k ld/X/K ld/sf{1 index type/stringtype eq{/s1t xd/s1n xd}{/s1t 0 def/s1n
_ def}ifelse/s1k xd/s1y xd/s1m xd/s1c xd}bd/i{dup 0 ne{setflat}{pop}ifelse}bd
/v{4 -2 roll 2 copy 6 -2 roll c}bd/V/v ld/y{2 copy c}bd/Y/y ld/@w{matrix rotate
/$ptm xd matrix scale $ptm dup concatmatrix/$ptm xd 1 eq{$ptm exch dup
concatmatrix/$ptm xd}if 1 w}bd/@g{1 eq dup/$sdf xd{/$scp xd/$sca xd/$scf xd}if
}bd/@G{1 eq dup/$SDF xd{/$SCP xd/$SCA xd/$SCF xd}if}bd/@D{2 index 0 eq{$dsf 3 1
roll 4 -1 roll pop}if 3 copy exch $Scra add exch load SetScr/$dsp xd/$dsa xd
/$dsf xd}bd/$ngx{$SDF{$SCF SepMode_5 0 eq{$SCA}{$dsa}ifelse $SCP @ss}if}bd/p{
/$pm xd 7 rp/$pyf xd/$pxf xd/$pn xd/$fil 1 def}bd/@MN{2 copy le{pop}{exch pop}
ifelse}bd/@MX{2 copy ge{pop}{exch pop}ifelse}bd/InRange{3 -1 roll @MN @MX}bd
/wDstChck{2 1 roll dup 3 -1 roll eq{1 add}if}bd/@dot{dup mul exch dup mul add 1
exch sub}bd/@lin{exch pop abs 1 exch sub}bd/cmyk2rgb{3{dup 5 -1 roll add 1 exch
sub dup 0 lt{pop 0}if exch}repeat pop}bd/rgb2cmyk{3{1 exch sub 3 1 roll}repeat
3 copy @MN @MN 3{dup 5 -1 roll sub neg exch}repeat}bd/rgb2g{2 index .299 mul 2
index .587 mul add 1 index .114 mul add 4 1 roll 3 rp}bd/WaldoColor_5 where{
pop}{/SetRgb/setrgbcolor ld/GetRgb/currentrgbcolor ld/SetGry/setgray ld/GetGry
/currentgray ld/SetRgb2 systemdict/setrgbcolor get def/GetRgb2 systemdict
/currentrgbcolor get def/SetHsb systemdict/sethsbcolor get def/GetHsb
systemdict/currenthsbcolor get def/rgb2hsb{SetRgb2 GetHsb}bd/hsb2rgb{3 -1 roll
dup floor sub 3 1 roll SetHsb GetRgb2}bd/setcmykcolor where{pop/SetCmyk_5
/setcmykcolor ld}{/SetCmyk_5{cmyk2rgb SetRgb}bd}ifelse/currentcmykcolor where{
pop/GetCmyk/currentcmykcolor ld}{/GetCmyk{GetRgb rgb2cmyk}bd}ifelse
/setoverprint where{pop}{/setoverprint{/$op xd}bd}ifelse/currentoverprint where
{pop}{/currentoverprint{$op}bd}ifelse/@tc_5{5 -1 roll dup 1 ge{pop}{4{dup 6 -1
roll mul exch}repeat pop}ifelse}bd/@trp{exch pop 5 1 roll @tc_5}bd
/setprocesscolor_5{SepMode_5 0 eq{SetCmyk_5}{0 4 $ink_5 sub index exch pop 5 1
roll 4 rp SepsColor true eq{$ink_5 3 gt{1 sub neg SetGry}{0 0 0 4 $ink_5 roll
SetCmyk_5}ifelse}{1 sub neg SetGry}ifelse}ifelse}bd/findcmykcustomcolor where
{pop}{/findcmykcustomcolor{5 array astore}bd}ifelse/setcustomcolor where{pop}{
/setcustomcolor{exch aload pop SepMode_5 0 eq{pop @tc_5 setprocesscolor_5}{
CurrentInkName_5 eq{4 index}{0}ifelse 6 1 roll 5 rp 1 sub neg SetGry}ifelse}bd
}ifelse/@scc_5{dup type/booleantype eq{setoverprint}{1 eq setoverprint}ifelse
dup _ eq{pop setprocesscolor_5 pop}{findcmykcustomcolor exch setcustomcolor}
ifelse SepMode_5 0 eq{true}{GetGry 1 eq currentoverprint and not}ifelse}bd
/colorimage where{pop/ColorImage/colorimage ld}{/ColorImage{/ncolors xd pop
/dataaq xd{dataaq ncolors dup 3 eq{/$dat xd 0 1 $dat length 3 div 1 sub{dup 3
mul $dat 1 index get 255 div $dat 2 index 1 add get 255 div $dat 3 index 2 add
get 255 div rgb2g 255 mul cvi exch pop $dat 3 1 roll put}for $dat 0 $dat length
3 idiv getinterval pop}{4 eq{/$dat xd 0 1 $dat length 4 div 1 sub{dup 4 mul
$dat 1 index get 255 div $dat 2 index 1 add get 255 div $dat 3 index 2 add get
255 div $dat 4 index 3 add get 255 div cmyk2rgb rgb2g 255 mul cvi exch pop $dat
3 1 roll put}for $dat 0 $dat length ncolors idiv getinterval}if}ifelse}image}
bd}ifelse/setcmykcolor{1 5 1 roll _ currentoverprint @scc_5/$ffpnt xd}bd
/currentcmykcolor{0 0 0 0}bd/setrgbcolor{rgb2cmyk setcmykcolor}bd
/currentrgbcolor{currentcmykcolor cmyk2rgb}bd/sethsbcolor{hsb2rgb setrgbcolor}
bd/currenthsbcolor{currentrgbcolor rgb2hsb}bd/setgray{dup dup setrgbcolor}bd
/currentgray{currentrgbcolor rgb2g}bd}ifelse/WaldoColor_5 true def/@sft{$tllx
$pxf add dup $tllx gt{$pwid sub}if/$tx xd $tury $pyf sub dup $tury lt{$phei
add}if/$ty xd}bd/@stb{pathbbox/$ury xd/$urx xd/$lly xd/$llx xd}bd/@ep{{cvx exec
}forall}bd/@tp{@sv/$in true def 2 copy dup $lly le{/$in false def}if $phei sub
$ury ge{/$in false def}if dup $urx ge{/$in false def}if $pwid add $llx le{/$in
false def}if $in{@np 2 copy m $pwid 0 rl 0 $phei neg rl $pwid neg 0 rl 0 $phei
rl clip @np $pn cvlit load aload pop 7 -1 roll 5 index sub 7 -1 roll 3 index
sub Tl matrix currentmatrix/$ctm xd @ep 4 rp}{2 rp}ifelse @rs}bd/@th{@sft 0 1
$tly 1 sub{dup $psx mul $tx add{dup $llx gt{$pwid sub}{exit}ifelse}loop exch
$phei mul $ty exch sub 0 1 $tlx 1 sub{$pwid mul 3 copy 3 -1 roll add exch @tp
pop}for 2 rp}for}bd/@tv{@sft 0 1 $tlx 1 sub{dup $pwid mul $tx add exch $psy mul
$ty exch sub{dup $ury lt{$phei add}{exit}ifelse}loop 0 1 $tly 1 sub{$phei mul 3
copy sub @tp pop}for 2 rp}for}bd/@pf{@gs $ctm setmatrix $pm concat @stb eoclip
Bburx Bbury $pm itransform/$tury xd/$turx xd Bbllx Bblly $pm itransform/$tlly
xd/$tllx xd/$wid $turx $tllx sub def/$hei $tury $tlly sub def @gs $vectpat{1 0
0 0 0 _ $o @scc_5{eofill}if}{$t $c $m $y $k $n $o @scc_5{SepMode_5 0 eq $pfrg
or{$tllx $tlly Tl $wid $hei scale <00> 8 1 false[8 0 0 1 0 0]{}imagemask}{
/$bkg true def}ifelse}if}ifelse @gr $wid 0 gt $hei 0 gt and{$pn cvlit load
aload pop/$pd xd 3 -1 roll sub/$phei xd exch sub/$pwid xd $wid $pwid div
ceiling 1 add/$tlx xd $hei $phei div ceiling 1 add/$tly xd $psx 0 eq{@tv}{@th}
ifelse}if @gr @np/$bkg false def}bd/@dlt{$fse $fss sub/nff xd $frb dup 1 eq
exch 2 eq or{$frt dup $frc $frm $fry $frk @tc_5 4 copy cmyk2rgb rgb2hsb 3 copy
/myb xd/mys xd/myh xd $tot $toc $tom $toy $tok @tc_5 cmyk2rgb rgb2hsb 3 1 roll
4 1 roll 5 1 roll sub neg nff div/kdb xd sub neg nff div/kds xd sub neg dup 0
eq{pop $frb 2 eq{.99}{-.99}ifelse}if dup $frb 2 eq exch 0 lt and{1 add}if dup
$frb 1 eq exch 0 gt and{1 sub}if nff div/kdh xd}{$frt dup $frc $frm $fry $frk
@tc_5 5 copy $tot dup $toc $tom $toy $tok @tc_5 5 1 roll 6 1 roll 7 1 roll 8 1
roll 9 1 roll sub neg nff dup 1 gt{1 sub}if div/$dk xd sub neg nff dup 1 gt{1
sub}if div/$dy xd sub neg nff dup 1 gt{1 sub}if div/$dm xd sub neg nff dup 1 gt
{1 sub}if div/$dc xd sub neg nff dup 1 gt{1 sub}if div/$dt xd}ifelse}bd/ffcol{
5 copy $fsit 0 eq{setcmykcolor pop}{SepMode_5 0 ne{$frn findcmykcustomcolor
exch setcustomcolor}{4 rp $frc $frm $fry $frk $frn findcmykcustomcolor exch
setcustomcolor}ifelse}ifelse}bd/@ftl{1 index 4 index sub dup $pad mul dup/$pdw
xd 2 mul sub $fst div/$wid xd 2 index sub/$hei xd pop Tl @dlt $fss 0 eq{ffcol n
0 0 m 0 $hei l $pdw $hei l $pdw 0 l @cp $ffpnt{fill}{@np}ifelse}if $fss $wid
mul $pdw add 0 Tl nff{ffcol n 0 0 m 0 $hei l $wid $hei l $wid 0 l @cp $ffpnt
{fill}{@np}ifelse $wid 0 Tl $frb dup 1 eq exch 2 eq or{4 rp myh mys myb kdb add
3 1 roll kds add 3 1 roll kdh add 3 1 roll 3 copy/myb xd/mys xd/myh xd hsb2rgb
rgb2cmyk}{$dk add 5 1 roll $dy add 5 1 roll $dm add 5 1 roll $dc add 5 1 roll
$dt add 5 1 roll}ifelse}repeat 5 rp $tot dup $toc $tom $toy $tok @tc_5 ffcol n
0 0 m 0 $hei l $pdw $hei l $pdw 0 l @cp $ffpnt{fill}{@np}ifelse 5 rp}bd/@ftrs{
1 index 4 index sub dup $rox mul/$row xd 2 div 1 index 4 index sub dup $roy mul
/$roh xd 2 div 2 copy dup mul exch dup mul add sqrt $row dup mul $roh dup mul
add sqrt add dup/$hei xd $fst div/$wid xd 4 index add $roh add exch 5 index add
$row add exch Tl $fan rotate 4 rp @dlt $fss 0 eq{ffcol $fty 3 eq{$hei dup neg
dup m 2 mul @sqr}{0 0 m 0 0 $hei 0 360 arc}ifelse $ffpnt{fill}{@np}ifelse}if
1.0 $pad 2 mul sub dup scale $hei $fss $wid mul sub/$hei xd nff{ffcol $fty 3 eq
{n $hei dup neg dup m 2 mul @sqr}{n 0 0 m 0 0 $hei 0 360 arc}ifelse $ffpnt
{fill}{@np}ifelse/$hei $hei $wid sub def $frb dup 1 eq exch 2 eq or{4 rp myh
mys myb kdb add 3 1 roll kds add 3 1 roll kdh add 3 1 roll 3 copy/myb xd/mys xd
/myh xd hsb2rgb rgb2cmyk}{$dk add 5 1 roll $dy add 5 1 roll $dm add 5 1 roll
$dc add 5 1 roll $dt add 5 1 roll}ifelse}repeat 5 rp}bd/@ftc{1 index 4 index
sub dup $rox mul/$row xd 2 div 1 index 4 index sub dup $roy mul/$roh xd 2 div 2
copy dup mul exch dup mul add sqrt $row dup mul $roh dup mul add sqrt add dup
/$hei xd $fst div/$wid xd 4 index add $roh add exch 5 index add $row add exch
Tl 4 rp @dlt $fss 0 eq{ffcol $ffpnt{fill}{@np}ifelse}{n}ifelse/$dang 180 $fst 1
sub div def/$sang $dang -2 div 180 add def/$eang $dang 2 div 180 add def/$sang
$sang $dang $fss mul add def/$eang $eang $dang $fss mul add def/$sang $eang
$dang sub def nff{ffcol n 0 0 m 0 0 $hei $sang $fan add $eang $fan add arc
$ffpnt{fill}{@np}ifelse 0 0 m 0 0 $hei $eang neg $fan add $sang neg $fan add
arc $ffpnt{fill}{@np}ifelse/$sang $eang def/$eang $eang $dang add def $frb dup
1 eq exch 2 eq or{4 rp myh mys myb kdb add 3 1 roll kds add 3 1 roll kdh add 3
1 roll 3 copy/myb xd/mys xd/myh xd hsb2rgb rgb2cmyk}{$dk add 5 1 roll $dy add 5
1 roll $dm add 5 1 roll $dc add 5 1 roll $dt add 5 1 roll}ifelse}repeat 5 rp}
bd/@ff{/$fss 0 def $o 1 eq setoverprint 1 1 $fsc 1 sub{dup 1 sub $fsit 0 eq{
$fsa exch 5 mul 5 getinterval aload 2 rp/$frk xd/$fry xd/$frm xd/$frc xd/$frn _
def/$frt 1 def $fsa exch 5 mul 5 getinterval aload pop $fss add/$fse xd/$tok xd
/$toy xd/$tom xd/$toc xd/$ton _ def/$tot 1 def}{$fsa exch 7 mul 7 getinterval
aload 2 rp/$frt xd/$frn xd/$frk xd/$fry xd/$frm xd/$frc xd $fsa exch 7 mul 7
getinterval aload pop $fss add/$fse xd/$tot xd/$ton xd/$tok xd/$toy xd/$tom xd
/$toc xd}ifelse $fsit 0 eq SepMode_5 0 eq or dup not CurrentInkName_5 $frn eq
and or{@sv $ctm setmatrix eoclip Bbllx Bblly Bburx Bbury $fty 2 eq{@ftc}{1
index 3 index m 2 copy l 3 index 1 index l 3 index 3 index l @cp $fty dup 1 eq
exch 3 eq or{@ftrs}{4 rp $fan rotate pathbbox @ftl}ifelse}ifelse @rs/$fss $fse
def}{1 0 0 0 0 _ $o @scc_5{fill}if}ifelse}for @np}bd/@Pf{@sv SepMode_5 0 eq
$ink_5 3 eq or{0 J 0 j[]0 d $t $c $m $y $k $n $o @scc_5 pop $ctm setmatrix 72
1000 div dup matrix scale dup concat dup Bburx exch Bbury exch itransform
ceiling cvi/Bbury xd ceiling cvi/Bburx xd Bbllx exch Bblly exch itransform
floor cvi/Bblly xd floor cvi/Bbllx xd $Prm aload pop $Psn load exec}{1 SetGry
eofill}ifelse @rs @np}bd/F{matrix currentmatrix $sdf{$scf $sca $scp @ss}if $fil
1 eq{@pf}{$fil 2 eq{@ff}{$fil 3 eq{@Pf}{$t $c $m $y $k $n $o @scc_5{eofill}
{@np}ifelse}ifelse}ifelse}ifelse $sdf{$dsf $dsa $dsp @ss}if setmatrix}bd/f{@cp
F}bd/S{matrix currentmatrix $ctm setmatrix $SDF{$SCF $SCA $SCP @ss}if $T $C $M
$Y $K $N $O @scc_5{matrix currentmatrix $ptm concat stroke setmatrix}
{@np}ifelse $SDF{$dsf $dsa $dsp @ss}if setmatrix}bd/s{@cp S}bd/B{@gs F @gr S}
bd/b{@cp B}bd/E{5 array astore exch cvlit xd}bd/@cc{currentfile $dat
readhexstring pop}bd/@sm{/$ctm $ctm currentmatrix def}bd/@E{/Bbury xd/Bburx xd
/Bblly xd/Bbllx xd}bd/@c{@cp}bd/@p{/$fil 1 def 1 eq dup/$vectpat xd{/$pfrg true
def}{@gs $t $c $m $y $k $n $o @scc_5/$pfrg xd @gr}ifelse/$pm xd/$psy xd/$psx xd
/$pyf xd/$pxf xd/$pn xd}bd/@P{/$fil 3 def/$Psn xd array astore/$Prm xd}bd/@k{
/$fil 2 def/$roy xd/$rox xd/$pad xd/$fty xd/$fan xd $fty 1 eq{/$fan 0 def}if
/$frb xd/$fst xd/$fsc xd/$fsa xd/$fsit 0 def}bd/@x{/$fil 2 def/$roy xd/$rox xd
/$pad xd/$fty xd/$fan xd $fty 1 eq{/$fan 0 def}if/$frb xd/$fst xd/$fsc xd/$fsa
xd/$fsit 1 def}bd/@ii{concat 3 index 3 index m 3 index 1 index l 2 copy l 1
index 3 index l 3 index 3 index l clip 4 rp}bd/tcc{@cc}def/@i{@sm @gs @ii 6
index 1 ne{/$frg true def 2 rp}{1 eq{s1t s1c s1m s1y s1k s1n $O @scc_5/$frg xd
}{/$frg false def}ifelse 1 eq{@gs $ctm setmatrix F @gr}if}ifelse @np/$ury xd
/$urx xd/$lly xd/$llx xd/$bts xd/$hei xd/$wid xd/$dat $wid $bts mul 8 div
ceiling cvi string def $bkg $frg or{$SDF{$SCF $SCA $SCP @ss}if $llx $lly Tl
$urx $llx sub $ury $lly sub scale $bkg{$t $c $m $y $k $n $o @scc_5 pop}if $wid
$hei abs $bts 1 eq{$bkg}{$bts}ifelse[$wid 0 0 $hei neg 0 $hei 0
gt{$hei}{0}ifelse]/tcc load $bts 1 eq{imagemask}{image}ifelse $SDF{$dsf $dsa
$dsp @ss}if}{$hei abs{tcc pop}repeat}ifelse @gr $ctm setmatrix}bd/@M{@sv}bd/@N
{/@cc{}def 1 eq{12 -1 roll neg 12 1 roll @I}{13 -1 roll neg 13 1 roll @i}
ifelse @rs}bd/@I{@sm @gs @ii @np/$ury xd/$urx xd/$lly xd/$llx xd/$ncl xd/$bts
xd/$hei xd/$wid xd/$dat $wid $bts mul $ncl mul 8 div ceiling cvi string def
$ngx $llx $lly Tl $urx $llx sub $ury $lly sub scale $wid $hei abs $bts[$wid 0 0
$hei neg 0 $hei 0 gt{$hei}{0}ifelse]/@cc load false $ncl ColorImage $SDF{$dsf
$dsa $dsp @ss}if @gr $ctm setmatrix}bd/z{exch findfont exch scalefont setfont}
bd/ZB{9 dict dup begin 4 1 roll/FontType 3 def/FontMatrix xd/FontBBox xd
/Encoding 256 array def 0 1 255{Encoding exch/.notdef put}for/CharStrings 256
dict def CharStrings/.notdef{}put/Metrics 256 dict def Metrics/.notdef 3 -1
roll put/BuildChar{exch dup/$char exch/Encoding get 3 index get def dup
/Metrics get $char get aload pop setcachedevice begin Encoding exch get
CharStrings exch get end exec}def end definefont pop}bd/ZBAddChar{findfont
begin dup 4 1 roll dup 6 1 roll Encoding 3 1 roll put CharStrings 3 1 roll put
Metrics 3 1 roll put end}bd/Z{findfont dup maxlength 2 add dict exch dup{1
index/FID ne{3 index 3 1 roll put}{2 rp}ifelse}forall pop dup dup/Encoding get
256 array copy dup/$fe xd/Encoding exch put dup/Fontname 3 index put 3 -1 roll
dup length 0 ne{0 exch{dup type 0 type eq{exch pop}{$fe exch 2 index exch put 1
add}ifelse}forall pop}if dup 256 dict dup/$met xd/Metrics exch put dup
/FontMatrix get 0 get 1000 mul 1 exch div 3 index length 256 eq{0 1 255{dup $fe
exch get dup/.notdef eq{2 rp}{5 index 3 -1 roll get 2 index mul $met 3 1 roll
put}ifelse}for}if pop definefont pop pop}bd/@ftx{{currentpoint 3 -1 roll(0)dup
3 -1 roll 0 exch put dup @gs true charpath $ctm setmatrix @@txt @gr @np
stringwidth pop 3 -1 roll add exch moveto}forall}bd/@ft{matrix currentmatrix
exch $sdf{$scf $sca $scp @ss}if $fil 1 eq{/@@txt/@pf ld @ftx}{$fil 2 eq{/@@txt
/@ff ld @ftx}{$fil 3 eq{/@@txt/@Pf ld @ftx}{$t $c $m $y $k $n $o @scc_5{show}
{pop}ifelse}ifelse}ifelse}ifelse $sdf{$dsf $dsa $dsp @ss}if setmatrix}bd/@st{
matrix currentmatrix exch $SDF{$SCF $SCA $SCP @ss}if $T $C $M $Y $K $N $O
@scc_5{{currentpoint 3 -1 roll(0)dup 3 -1 roll 0 exch put dup @gs true charpath
$ctm setmatrix $ptm concat stroke @gr @np stringwidth pop 3 -1 roll add exch
moveto}forall}{pop}ifelse $SDF{$dsf $dsa $dsp @ss}if setmatrix}bd/@te{@ft}bd
/@tr{@st}bd/@ta{dup @gs @ft @gr @st}bd/@t@a{dup @gs @st @gr @ft}bd/@tm{@sm
concat}bd/e{/t{@te}def}bd/r{/t{@tr}def}bd/o{/t{pop}def}bd/a{/t{@ta}def}bd/@a{
/t{@t@a}def}bd/t{@te}def/T{@np $ctm setmatrix/$ttm matrix def}bd/ddt{t}def/@t{
/$stm $stm currentmatrix def 3 1 roll moveto $ttm concat ddt $stm setmatrix}bd
/@n{/$ttm exch matrix rotate def}bd/@s{}bd/@l{}bd/@B{@gs S @gr F}bd/@b{@cp @B}
bd/@sep{CurrentInkName_5(Composite)eq{/$ink_5 -1 def}{CurrentInkName_5(Cyan)eq
{/$ink_5 0 def}{CurrentInkName_5(Magenta)eq{/$ink_5 1 def}{CurrentInkName_5
(Yellow)eq{/$ink_5 2 def}{CurrentInkName_5(Black)eq{/$ink_5 3 def}{/$ink_5 4
def}ifelse}ifelse}ifelse}ifelse}ifelse}bd/@whi{@gs -72000 dup moveto -72000
72000 lineto 72000 dup lineto 72000 -72000 lineto @cp 1 SetGry fill @gr}bd
/@neg{[{1 exch sub}/exec cvx currenttransfer/exec cvx]cvx settransfer @whi}bd
/currentscale{1 0 dtransform matrix defaultmatrix idtransform dup mul exch dup
mul add sqrt 0 1 dtransform matrix defaultmatrix idtransform dup mul exch dup
mul add sqrt}bd/@unscale{currentscale 1 exch div exch 1 exch div exch scale}bd
/@sqr{dup 0 rlineto dup 0 exch rlineto neg 0 rlineto @cp}bd/corelsym{@gs @np Tl
-90 rotate 7{45 rotate -.75 2 moveto 1.5 @sqr fill}repeat @gr}bd/@reg_cor{@gs
@np Tl -6 -6 moveto 12 @sqr @gs 1 GetGry sub SetGry fill @gr 4{90 rotate 0 4 m
0 4 rl}repeat stroke 0 0 corelsym @gr}bd/@reg_std{@gs @np Tl .3 w 0 0 5 0 360
arc @cp @gs 1 GetGry sub SetGry fill @gr 4{90 rotate 0 0 m 0 8 rl}repeat stroke
@gr}bd/@reg_inv{@gs @np Tl .3 w 0 0 5 0 360 arc @cp @gs 1 GetGry sub SetGry
fill @gr 4{90 rotate 0 0 m 0 8 rl}repeat stroke 0 0 m 0 0 5 90 180 arc @cp 0 0
5 270 360 arc @cp GetGry fill @gr}bd/$corelmeter[1 .95 .75 .50 .25 .05 0]def
/@colormeter{@gs @np 0 SetGry 0.3 w/Courier findfont 5 scalefont setfont/yy xd
/xx xd 0 1 6{dup xx 20 sub yy m 20 @sqr @gs $corelmeter exch get dup SetGry
fill @gr stroke xx 18 sub yy 2 add m exch dup 3 ge{1 SetGry}{0 SetGry}ifelse 3
eq{pop}{100 mul 100 exch sub cvi 20 string cvs show}ifelse/yy yy 20 add def}
for @gr}bd/@calbar{@gs Tl @gs @np 0 0 m @gs 20 @sqr 1 1 0 0 0 _ 0 @scc_5 pop
fill @gr 20 0 Tl 0 0 m @gs 20 @sqr 1 1 0 1 0 _ 0 @scc_5 pop fill @gr 20 0 Tl 0
0 m @gs 20 @sqr 1 0 0 1 0 _ 0 @scc_5 pop fill @gr 20 0 Tl 0 0 m @gs 20 @sqr 1 0
1 1 0 _ 0 @scc_5 pop fill @gr 20 0 Tl 0 0 m @gs 20 @sqr 1 0 1 0 0 _ 0 @scc_5
pop fill @gr 20 0 Tl 0 0 m @gs 20 @sqr 1 1 1 0 0 _ 0 @scc_5 pop fill @gr 20 0
Tl 0 0 m @gs 20 @sqr 1 1 1 1 0 _ 0 @scc_5 pop fill @gr @gr @np -84 0 Tl @gs 0 0
m 20 @sqr clip 1 1 0 0 0 _ 0 @scc_5 pop @gain @gr 20 0 Tl @gs 0 0 m 20 @sqr
clip 1 0 1 0 0 _ 0 @scc_5 pop @gain @gr 20 0 Tl @gs 0 0 m 20 @sqr clip 1 0 0 1
0 _ 0 @scc_5 pop @gain @gr 20 0 Tl @gs 0 0 m 20 @sqr clip 1 0 0 0 1 _ 0 @scc_5
pop @gain @gr @gr}bd/@gain{10 10 Tl @np 0 0 m 0 10 360{0 0 15 4 -1 roll dup 5
add arc @cp}for fill}bd/@crop{@gs 10 div/$croplen xd .3 w 0 SetGry Tl rotate 0
0 m 0 $croplen neg rl stroke @gr}bd/@colorbox{@gs @np Tl 100 exch sub 100 div
SetGry -8 -8 moveto 16 @sqr fill 0 SetGry 10 -2 moveto show @gr}bd/deflevel 0
def/@sax{/deflevel deflevel 1 add def}bd/@eax{/deflevel deflevel dup 0 gt{1
sub}if def deflevel 0 gt{/eax load}{eax}ifelse}bd/eax{{exec}forall}bd/@rax{
deflevel 0 eq{@rs @sv}if}bd/@daq{dup type/arraytype eq{{}forall}if}bd/@BMP{
/@cc xd 11 index 1 eq{12 -1 roll pop @i}{7 -2 roll 2 rp @I}ifelse}bd systemdict
/pdfmark known not{/pdfmark/cleartomark ld}if end
%%EndResource
%%EndProlog
%%BeginSetup
wCorel5Dict begin
@BeginSysCorelDict
2.6131 setmiterlimit
1.00 setflat
/$fst 20 def
%%EndSetup

%StartPage
@sv
@sm
@sv
%StartColorLayer (COMPOSITE)
@sm @sv @sv
@rax %Note: Object
181.58 325.66 317.81 518.18 @E
0 J 0 j [] 0 d 0 R 0 @G
0.00 0.00 0.00 1.00 K
0 0.864 0.864 0.000 @w
181.58 518.18 m
317.81 518.18 L
317.81 325.66 L
181.58 325.66 L
181.58 518.18 L
@c
S

@rax 241.06 496.51 260.50 503.71 @E
[0.03384 0.00000 0.00000 0.03384 241.03295 498.19534] @tm
 0 O 0 @g
0.00 0.00 0.00 1.00 k
e

% FontChange:/_Times-Roman 235.00 z
%CHAR: 0 0 (U) @t
111 151 m
111 155 L
164 155 L
164 151 L
157 151 l
153 151 147 147 145 143 C
145 140 143 134 143 128 c
143 64 l
143 49 140 36 138 28 c
134 19 128 11 119 6 c
111 2 98 -2 83 -2 c
66 -2 53 2 45 6 c
36 11 30 21 26 30 C
26 34 23 49 23 66 c
23 128 l
23 136 21 143 19 145 C
17 149 11 151 6 151 c
2 151 L
2 155 L
66 155 L
66 151 L
60 151 l
55 151 49 149 47 145 C
47 143 45 136 45 128 c
45 57 l
45 53 45 45 45 38 c
45 30 49 23 51 19 c
53 15 60 11 64 9 c
68 6 77 4 83 4 c
91 4 102 6 109 11 c
117 15 123 21 126 28 C
130 34 132 47 132 64 c
132 128 l
132 136 130 143 128 145 C
126 149 119 151 115 151 c
111 151 L
@c
F
%CHAR: 217 0 (\050) @t
289 -47 m
289 -49 L
277 -45 266 -36 260 -30 c
249 -19 238 -4 234 9 c
230 23 226 40 226 55 c
226 79 232 102 243 121 c
253 140 270 155 289 164 C
289 160 L
281 155 270 147 266 138 c
260 130 253 117 251 104 c
249 91 247 74 247 60 c
247 43 249 28 249 15 c
251 6 255 -4 257 -9 c
260 -15 264 -23 268 -28 c
272 -34 281 -40 289 -45 C
289 -47 L
@c
F
%CHAR: 342 0 (6) @t
444 157 m
444 153 L
436 153 425 149 421 147 c
414 145 406 138 402 134 c
395 128 389 119 385 113 c
380 104 376 94 374 85 C
385 94 397 98 408 98 c
419 98 429 94 436 85 c
444 77 448 64 448 51 c
448 38 444 26 436 17 c
427 4 412 -2 397 -2 c
389 -2 378 2 372 6 c
359 19 351 38 351 60 c
351 72 353 87 357 98 c
361 111 372 123 380 132 c
391 140 402 149 410 151 c
419 155 429 157 436 157 c
444 157 L
@c
374 77 m
374 68 372 57 372 51 c
372 45 374 36 374 28 c
376 19 382 13 387 9 C
391 6 397 4 402 4 c
406 4 414 9 419 13 c
423 17 427 28 427 38 c
427 51 423 64 419 72 c
414 81 406 87 397 87 c
395 87 391 85 389 85 c
385 85 378 81 374 77 C
@c
F
%CHAR: 507 0 (\051) @t
513 160 m
513 164 L
524 157 535 149 541 143 c
552 132 562 117 567 104 c
573 89 577 72 577 57 c
577 34 571 11 560 -9 c
547 -28 530 -43 513 -49 C
513 -45 L
522 -40 530 -34 535 -26 c
541 -17 547 -4 550 9 c
554 21 556 38 556 53 c
556 70 554 85 552 98 c
550 106 545 117 543 121 c
541 128 537 136 533 140 c
528 147 520 155 513 160 C
@c
F
T
@rax 241.06 496.51 260.50 503.71 @E
[0.03384 0.00000 0.00000 0.03384 241.03295 498.19534] @tm
0 J 2 j [] 0 d 0 R 0 @G
0.00 0.00 0.00 1.00 K
0 0.072 0.072 0.000 @w
r

% FontChange:/_Times-Roman 235.00 z
%CHAR: 0 0 (U) @t
111 151 m
111 155 L
164 155 L
164 151 L
157 151 l
153 151 147 147 145 143 C
145 140 143 134 143 128 c
143 64 l
143 49 140 36 138 28 c
134 19 128 11 119 6 c
111 2 98 -2 83 -2 c
66 -2 53 2 45 6 c
36 11 30 21 26 30 C
26 34 23 49 23 66 c
23 128 l
23 136 21 143 19 145 C
17 149 11 151 6 151 c
2 151 L
2 155 L
66 155 L
66 151 L
60 151 l
55 151 49 149 47 145 C
47 143 45 136 45 128 c
45 57 l
45 53 45 45 45 38 c
45 30 49 23 51 19 c
53 15 60 11 64 9 c
68 6 77 4 83 4 c
91 4 102 6 109 11 c
117 15 123 21 126 28 C
130 34 132 47 132 64 c
132 128 l
132 136 130 143 128 145 C
126 149 119 151 115 151 c
111 151 L
@c
S
%CHAR: 217 0 (\050) @t
289 -47 m
289 -49 L
277 -45 266 -36 260 -30 c
249 -19 238 -4 234 9 c
230 23 226 40 226 55 c
226 79 232 102 243 121 c
253 140 270 155 289 164 C
289 160 L
281 155 270 147 266 138 c
260 130 253 117 251 104 c
249 91 247 74 247 60 c
247 43 249 28 249 15 c
251 6 255 -4 257 -9 c
260 -15 264 -23 268 -28 c
272 -34 281 -40 289 -45 C
289 -47 L
@c
S
%CHAR: 342 0 (6) @t
444 157 m
444 153 L
436 153 425 149 421 147 c
414 145 406 138 402 134 c
395 128 389 119 385 113 c
380 104 376 94 374 85 C
385 94 397 98 408 98 c
419 98 429 94 436 85 c
444 77 448 64 448 51 c
448 38 444 26 436 17 c
427 4 412 -2 397 -2 c
389 -2 378 2 372 6 c
359 19 351 38 351 60 c
351 72 353 87 357 98 c
361 111 372 123 380 132 c
391 140 402 149 410 151 c
419 155 429 157 436 157 c
444 157 L
@c
374 77 m
374 68 372 57 372 51 c
372 45 374 36 374 28 c
376 19 382 13 387 9 C
391 6 397 4 402 4 c
406 4 414 9 419 13 c
423 17 427 28 427 38 c
427 51 423 64 419 72 c
414 81 406 87 397 87 c
395 87 391 85 389 85 c
385 85 378 81 374 77 C
@c
S
%CHAR: 507 0 (\051) @t
513 160 m
513 164 L
524 157 535 149 541 143 c
552 132 562 117 567 104 c
573 89 577 72 577 57 c
577 34 571 11 560 -9 c
547 -28 530 -43 513 -49 C
513 -45 L
522 -40 530 -34 535 -26 c
541 -17 547 -4 550 9 c
554 21 556 38 556 53 c
556 70 554 85 552 98 c
550 106 545 117 543 121 c
541 128 537 136 533 140 c
528 147 520 155 513 160 C
@c
S
T
@rax 192.53 446.90 212.04 454.10 @E
[0.03384 0.00000 0.00000 0.03384 192.54023 448.61974] @tm
 0 O 0 @g
0.00 0.00 0.00 1.00 k
e

% FontChange:/_Times-Roman 235.00 z
%CHAR: 0 0 (U) @t
111 151 m
111 155 L
164 155 L
164 151 L
157 151 l
153 151 147 147 145 143 C
145 140 143 134 143 128 c
143 64 l
143 49 140 36 138 28 c
134 19 128 11 119 6 c
111 2 98 -2 83 -2 c
66 -2 53 2 45 6 c
36 11 30 21 26 30 C
26 34 23 49 23 66 c
23 128 l
23 136 21 143 19 145 C
17 149 11 151 6 151 c
2 151 L
2 155 L
66 155 L
66 151 L
60 151 l
55 151 49 149 47 145 C
47 143 45 136 45 128 c
45 57 l
45 53 45 45 45 38 c
45 30 49 23 51 19 c
53 15 60 11 64 9 c
68 6 77 4 83 4 c
91 4 102 6 109 11 c
117 15 123 21 126 28 C
130 34 132 47 132 64 c
132 128 l
132 136 130 143 128 145 C
126 149 119 151 115 151 c
111 151 L
@c
F
%CHAR: 217 0 (\050) @t
289 -47 m
289 -49 L
277 -45 266 -36 260 -30 c
249 -19 238 -4 234 9 c
230 23 226 40 226 55 c
226 79 232 102 243 121 c
253 140 270 155 289 164 C
289 160 L
281 155 270 147 266 138 c
260 130 253 117 251 104 c
249 91 247 74 247 60 c
247 43 249 28 249 15 c
251 6 255 -4 257 -9 c
260 -15 264 -23 268 -28 c
272 -34 281 -40 289 -45 C
289 -47 L
@c
F
%CHAR: 342 0 (5) @t
442 155 m
431 136 L
385 136 L
374 115 L
395 113 414 104 425 91 c
433 83 440 68 440 55 c
440 47 438 38 436 32 C
431 26 427 19 423 15 c
419 11 410 6 406 2 C
397 0 389 -2 380 -2 c
372 -2 363 0 361 2 C
357 4 355 9 355 11 c
355 13 357 17 357 17 c
357 17 361 19 363 19 c
363 19 368 19 368 19 c
368 19 372 15 376 15 C
380 11 387 9 391 9 c
399 9 408 13 414 17 c
421 23 425 34 425 43 c
425 51 421 62 416 68 c
410 77 399 85 391 89 c
382 91 370 96 357 96 C
387 155 L
442 155 L
@c
F
%CHAR: 507 0 (\051) @t
513 160 m
513 164 L
524 157 535 149 541 143 c
552 132 562 117 567 104 c
573 89 577 72 577 57 c
577 34 571 11 560 -9 c
547 -28 530 -43 513 -49 C
513 -45 L
522 -40 530 -34 535 -26 c
541 -17 547 -4 550 9 c
554 21 556 38 556 53 c
556 70 554 85 552 98 c
550 106 545 117 543 121 c
541 128 537 136 533 140 c
528 147 520 155 513 160 C
@c
F
T
@rax 192.53 446.90 212.04 454.10 @E
[0.03384 0.00000 0.00000 0.03384 192.54023 448.61974] @tm
0 J 2 j [] 0 d 0 R 0 @G
0.00 0.00 0.00 1.00 K
0 0.072 0.072 0.000 @w
r

% FontChange:/_Times-Roman 235.00 z
%CHAR: 0 0 (U) @t
111 151 m
111 155 L
164 155 L
164 151 L
157 151 l
153 151 147 147 145 143 C
145 140 143 134 143 128 c
143 64 l
143 49 140 36 138 28 c
134 19 128 11 119 6 c
111 2 98 -2 83 -2 c
66 -2 53 2 45 6 c
36 11 30 21 26 30 C
26 34 23 49 23 66 c
23 128 l
23 136 21 143 19 145 C
17 149 11 151 6 151 c
2 151 L
2 155 L
66 155 L
66 151 L
60 151 l
55 151 49 149 47 145 C
47 143 45 136 45 128 c
45 57 l
45 53 45 45 45 38 c
45 30 49 23 51 19 c
53 15 60 11 64 9 c
68 6 77 4 83 4 c
91 4 102 6 109 11 c
117 15 123 21 126 28 C
130 34 132 47 132 64 c
132 128 l
132 136 130 143 128 145 C
126 149 119 151 115 151 c
111 151 L
@c
S
%CHAR: 217 0 (\050) @t
289 -47 m
289 -49 L
277 -45 266 -36 260 -30 c
249 -19 238 -4 234 9 c
230 23 226 40 226 55 c
226 79 232 102 243 121 c
253 140 270 155 289 164 C
289 160 L
281 155 270 147 266 138 c
260 130 253 117 251 104 c
249 91 247 74 247 60 c
247 43 249 28 249 15 c
251 6 255 -4 257 -9 c
260 -15 264 -23 268 -28 c
272 -34 281 -40 289 -45 C
289 -47 L
@c
S
%CHAR: 342 0 (5) @t
442 155 m
431 136 L
385 136 L
374 115 L
395 113 414 104 425 91 c
433 83 440 68 440 55 c
440 47 438 38 436 32 C
431 26 427 19 423 15 c
419 11 410 6 406 2 C
397 0 389 -2 380 -2 c
372 -2 363 0 361 2 C
357 4 355 9 355 11 c
355 13 357 17 357 17 c
357 17 361 19 363 19 c
363 19 368 19 368 19 c
368 19 372 15 376 15 C
380 11 387 9 391 9 c
399 9 408 13 414 17 c
421 23 425 34 425 43 c
425 51 421 62 416 68 c
410 77 399 85 391 89 c
382 91 370 96 357 96 C
387 155 L
442 155 L
@c
S
%CHAR: 507 0 (\051) @t
513 160 m
513 164 L
524 157 535 149 541 143 c
552 132 562 117 567 104 c
573 89 577 72 577 57 c
577 34 571 11 560 -9 c
547 -28 530 -43 513 -49 C
513 -45 L
522 -40 530 -34 535 -26 c
541 -17 547 -4 550 9 c
554 21 556 38 556 53 c
556 70 554 85 552 98 c
550 106 545 117 543 121 c
541 128 537 136 533 140 c
528 147 520 155 513 160 C
@c
S
T
@rax 238.90 446.90 258.34 454.10 @E
[0.03384 0.00000 0.00000 0.03384 238.86719 448.61974] @tm
 0 O 0 @g
0.00 0.00 0.00 1.00 k
e

% FontChange:/_Times-Roman 235.00 z
%CHAR: 0 0 (O) @t
85 157 m
104 157 123 149 136 134 c
151 119 160 98 160 77 c
160 55 151 34 138 21 c
123 6 104 -2 83 -2 c
62 -2 43 6 30 19 c
17 34 9 55 9 77 c
9 100 17 121 32 136 c
45 149 64 157 85 157 c
@c
83 151 m
70 151 57 145 49 134 c
40 121 34 102 34 79 c
34 53 40 32 49 19 c
57 11 70 4 83 4 c
96 4 111 11 119 21 c
128 32 134 51 134 74 c
134 100 128 121 117 134 c
109 145 96 151 83 151 c
@c
F
%CHAR: 217 0 (\050) @t
289 -47 m
289 -49 L
277 -45 266 -36 260 -30 c
249 -19 238 -4 234 9 c
230 23 226 40 226 55 c
226 79 232 102 243 121 c
253 140 270 155 289 164 C
289 160 L
281 155 270 147 266 138 c
260 130 253 117 251 104 c
249 91 247 74 247 60 c
247 43 249 28 249 15 c
251 6 255 -4 257 -9 c
260 -15 264 -23 268 -28 c
272 -34 281 -40 289 -45 C
289 -47 L
@c
F
%CHAR: 342 0 (6) @t
444 157 m
444 153 L
436 153 425 149 421 147 c
414 145 406 138 402 134 c
395 128 389 119 385 113 c
380 104 376 94 374 85 C
385 94 397 98 408 98 c
419 98 429 94 436 85 c
444 77 448 64 448 51 c
448 38 444 26 436 17 c
427 4 412 -2 397 -2 c
389 -2 378 2 372 6 c
359 19 351 38 351 60 c
351 72 353 87 357 98 c
361 111 372 123 380 132 c
391 140 402 149 410 151 c
419 155 429 157 436 157 c
444 157 L
@c
374 77 m
374 68 372 57 372 51 c
372 45 374 36 374 28 c
376 19 382 13 387 9 C
391 6 397 4 402 4 c
406 4 414 9 419 13 c
423 17 427 28 427 38 c
427 51 423 64 419 72 c
414 81 406 87 397 87 c
395 87 391 85 389 85 c
385 85 378 81 374 77 C
@c
F
%CHAR: 507 0 (\051) @t
513 160 m
513 164 L
524 157 535 149 541 143 c
552 132 562 117 567 104 c
573 89 577 72 577 57 c
577 34 571 11 560 -9 c
547 -28 530 -43 513 -49 C
513 -45 L
522 -40 530 -34 535 -26 c
541 -17 547 -4 550 9 c
554 21 556 38 556 53 c
556 70 554 85 552 98 c
550 106 545 117 543 121 c
541 128 537 136 533 140 c
528 147 520 155 513 160 C
@c
F
T
@rax 238.90 446.90 258.34 454.10 @E
[0.03384 0.00000 0.00000 0.03384 238.86719 448.61974] @tm
0 J 2 j [] 0 d 0 R 0 @G
0.00 0.00 0.00 1.00 K
0 0.072 0.072 0.000 @w
r

% FontChange:/_Times-Roman 235.00 z
%CHAR: 0 0 (O) @t
85 157 m
104 157 123 149 136 134 c
151 119 160 98 160 77 c
160 55 151 34 138 21 c
123 6 104 -2 83 -2 c
62 -2 43 6 30 19 c
17 34 9 55 9 77 c
9 100 17 121 32 136 c
45 149 64 157 85 157 c
@c
83 151 m
70 151 57 145 49 134 c
40 121 34 102 34 79 c
34 53 40 32 49 19 c
57 11 70 4 83 4 c
96 4 111 11 119 21 c
128 32 134 51 134 74 c
134 100 128 121 117 134 c
109 145 96 151 83 151 c
@c
S
%CHAR: 217 0 (\050) @t
289 -47 m
289 -49 L
277 -45 266 -36 260 -30 c
249 -19 238 -4 234 9 c
230 23 226 40 226 55 c
226 79 232 102 243 121 c
253 140 270 155 289 164 C
289 160 L
281 155 270 147 266 138 c
260 130 253 117 251 104 c
249 91 247 74 247 60 c
247 43 249 28 249 15 c
251 6 255 -4 257 -9 c
260 -15 264 -23 268 -28 c
272 -34 281 -40 289 -45 C
289 -47 L
@c
S
%CHAR: 342 0 (6) @t
444 157 m
444 153 L
436 153 425 149 421 147 c
414 145 406 138 402 134 c
395 128 389 119 385 113 c
380 104 376 94 374 85 C
385 94 397 98 408 98 c
419 98 429 94 436 85 c
444 77 448 64 448 51 c
448 38 444 26 436 17 c
427 4 412 -2 397 -2 c
389 -2 378 2 372 6 c
359 19 351 38 351 60 c
351 72 353 87 357 98 c
361 111 372 123 380 132 c
391 140 402 149 410 151 c
419 155 429 157 436 157 c
444 157 L
@c
374 77 m
374 68 372 57 372 51 c
372 45 374 36 374 28 c
376 19 382 13 387 9 C
391 6 397 4 402 4 c
406 4 414 9 419 13 c
423 17 427 28 427 38 c
427 51 423 64 419 72 c
414 81 406 87 397 87 c
395 87 391 85 389 85 c
385 85 378 81 374 77 C
@c
S
%CHAR: 507 0 (\051) @t
513 160 m
513 164 L
524 157 535 149 541 143 c
552 132 562 117 567 104 c
573 89 577 72 577 57 c
577 34 571 11 560 -9 c
547 -28 530 -43 513 -49 C
513 -45 L
522 -40 530 -34 535 -26 c
541 -17 547 -4 550 9 c
554 21 556 38 556 53 c
556 70 554 85 552 98 c
550 106 545 117 543 121 c
541 128 537 136 533 140 c
528 147 520 155 513 160 C
@c
S
T
@rax 190.87 400.18 210.31 407.38 @E
[0.03384 0.00000 0.00000 0.03384 190.84823 401.85286] @tm
 0 O 0 @g
0.00 0.00 0.00 1.00 k
e

% FontChange:/_Times-Roman 235.00 z
%CHAR: 0 0 (O) @t
85 157 m
104 157 123 149 136 134 c
151 119 160 98 160 77 c
160 55 151 34 138 21 c
123 6 104 -2 83 -2 c
62 -2 43 6 30 19 c
17 34 9 55 9 77 c
9 100 17 121 32 136 c
45 149 64 157 85 157 c
@c
83 151 m
70 151 57 145 49 134 c
40 121 34 102 34 79 c
34 53 40 32 49 19 c
57 11 70 4 83 4 c
96 4 111 11 119 21 c
128 32 134 51 134 74 c
134 100 128 121 117 134 c
109 145 96 151 83 151 c
@c
F
%CHAR: 217 0 (\050) @t
289 -47 m
289 -49 L
277 -45 266 -36 260 -30 c
249 -19 238 -4 234 9 c
230 23 226 40 226 55 c
226 79 232 102 243 121 c
253 140 270 155 289 164 C
289 160 L
281 155 270 147 266 138 c
260 130 253 117 251 104 c
249 91 247 74 247 60 c
247 43 249 28 249 15 c
251 6 255 -4 257 -9 c
260 -15 264 -23 268 -28 c
272 -34 281 -40 289 -45 C
289 -47 L
@c
F
%CHAR: 342 0 (5) @t
442 155 m
431 136 L
385 136 L
374 115 L
395 113 414 104 425 91 c
433 83 440 68 440 55 c
440 47 438 38 436 32 C
431 26 427 19 423 15 c
419 11 410 6 406 2 C
397 0 389 -2 380 -2 c
372 -2 363 0 361 2 C
357 4 355 9 355 11 c
355 13 357 17 357 17 c
357 17 361 19 363 19 c
363 19 368 19 368 19 c
368 19 372 15 376 15 C
380 11 387 9 391 9 c
399 9 408 13 414 17 c
421 23 425 34 425 43 c
425 51 421 62 416 68 c
410 77 399 85 391 89 c
382 91 370 96 357 96 C
387 155 L
442 155 L
@c
F
%CHAR: 507 0 (\051) @t
513 160 m
513 164 L
524 157 535 149 541 143 c
552 132 562 117 567 104 c
573 89 577 72 577 57 c
577 34 571 11 560 -9 c
547 -28 530 -43 513 -49 C
513 -45 L
522 -40 530 -34 535 -26 c
541 -17 547 -4 550 9 c
554 21 556 38 556 53 c
556 70 554 85 552 98 c
550 106 545 117 543 121 c
541 128 537 136 533 140 c
528 147 520 155 513 160 C
@c
F
T
@rax 190.87 400.18 210.31 407.38 @E
[0.03384 0.00000 0.00000 0.03384 190.84823 401.85286] @tm
0 J 2 j [] 0 d 0 R 0 @G
0.00 0.00 0.00 1.00 K
0 0.072 0.072 0.000 @w
r

% FontChange:/_Times-Roman 235.00 z
%CHAR: 0 0 (O) @t
85 157 m
104 157 123 149 136 134 c
151 119 160 98 160 77 c
160 55 151 34 138 21 c
123 6 104 -2 83 -2 c
62 -2 43 6 30 19 c
17 34 9 55 9 77 c
9 100 17 121 32 136 c
45 149 64 157 85 157 c
@c
83 151 m
70 151 57 145 49 134 c
40 121 34 102 34 79 c
34 53 40 32 49 19 c
57 11 70 4 83 4 c
96 4 111 11 119 21 c
128 32 134 51 134 74 c
134 100 128 121 117 134 c
109 145 96 151 83 151 c
@c
S
%CHAR: 217 0 (\050) @t
289 -47 m
289 -49 L
277 -45 266 -36 260 -30 c
249 -19 238 -4 234 9 c
230 23 226 40 226 55 c
226 79 232 102 243 121 c
253 140 270 155 289 164 C
289 160 L
281 155 270 147 266 138 c
260 130 253 117 251 104 c
249 91 247 74 247 60 c
247 43 249 28 249 15 c
251 6 255 -4 257 -9 c
260 -15 264 -23 268 -28 c
272 -34 281 -40 289 -45 C
289 -47 L
@c
S
%CHAR: 342 0 (5) @t
442 155 m
431 136 L
385 136 L
374 115 L
395 113 414 104 425 91 c
433 83 440 68 440 55 c
440 47 438 38 436 32 C
431 26 427 19 423 15 c
419 11 410 6 406 2 C
397 0 389 -2 380 -2 c
372 -2 363 0 361 2 C
357 4 355 9 355 11 c
355 13 357 17 357 17 c
357 17 361 19 363 19 c
363 19 368 19 368 19 c
368 19 372 15 376 15 C
380 11 387 9 391 9 c
399 9 408 13 414 17 c
421 23 425 34 425 43 c
425 51 421 62 416 68 c
410 77 399 85 391 89 c
382 91 370 96 357 96 C
387 155 L
442 155 L
@c
S
%CHAR: 507 0 (\051) @t
513 160 m
513 164 L
524 157 535 149 541 143 c
552 132 562 117 567 104 c
573 89 577 72 577 57 c
577 34 571 11 560 -9 c
547 -28 530 -43 513 -49 C
513 -45 L
522 -40 530 -34 535 -26 c
541 -17 547 -4 550 9 c
554 21 556 38 556 53 c
556 70 554 85 552 98 c
550 106 545 117 543 121 c
541 128 537 136 533 140 c
528 147 520 155 513 160 C
@c
S
T
@rax 239.69 400.18 259.20 407.38 @E
[0.03384 0.00000 0.00000 0.03384 239.71319 401.85286] @tm
 0 O 0 @g
0.00 0.00 0.00 1.00 k
e

% FontChange:/_Times-Roman 235.00 z
%CHAR: 0 0 (O) @t
85 157 m
104 157 123 149 136 134 c
151 119 160 98 160 77 c
160 55 151 34 138 21 c
123 6 104 -2 83 -2 c
62 -2 43 6 30 19 c
17 34 9 55 9 77 c
9 100 17 121 32 136 c
45 149 64 157 85 157 c
@c
83 151 m
70 151 57 145 49 134 c
40 121 34 102 34 79 c
34 53 40 32 49 19 c
57 11 70 4 83 4 c
96 4 111 11 119 21 c
128 32 134 51 134 74 c
134 100 128 121 117 134 c
109 145 96 151 83 151 c
@c
F
%CHAR: 217 0 (\050) @t
289 -47 m
289 -49 L
277 -45 266 -36 260 -30 c
249 -19 238 -4 234 9 c
230 23 226 40 226 55 c
226 79 232 102 243 121 c
253 140 270 155 289 164 C
289 160 L
281 155 270 147 266 138 c
260 130 253 117 251 104 c
249 91 247 74 247 60 c
247 43 249 28 249 15 c
251 6 255 -4 257 -9 c
260 -15 264 -23 268 -28 c
272 -34 281 -40 289 -45 C
289 -47 L
@c
F
%CHAR: 342 0 (5) @t
442 155 m
431 136 L
385 136 L
374 115 L
395 113 414 104 425 91 c
433 83 440 68 440 55 c
440 47 438 38 436 32 C
431 26 427 19 423 15 c
419 11 410 6 406 2 C
397 0 389 -2 380 -2 c
372 -2 363 0 361 2 C
357 4 355 9 355 11 c
355 13 357 17 357 17 c
357 17 361 19 363 19 c
363 19 368 19 368 19 c
368 19 372 15 376 15 C
380 11 387 9 391 9 c
399 9 408 13 414 17 c
421 23 425 34 425 43 c
425 51 421 62 416 68 c
410 77 399 85 391 89 c
382 91 370 96 357 96 C
387 155 L
442 155 L
@c
F
%CHAR: 507 0 (\051) @t
513 160 m
513 164 L
524 157 535 149 541 143 c
552 132 562 117 567 104 c
573 89 577 72 577 57 c
577 34 571 11 560 -9 c
547 -28 530 -43 513 -49 C
513 -45 L
522 -40 530 -34 535 -26 c
541 -17 547 -4 550 9 c
554 21 556 38 556 53 c
556 70 554 85 552 98 c
550 106 545 117 543 121 c
541 128 537 136 533 140 c
528 147 520 155 513 160 C
@c
F
T
@rax 239.69 400.18 259.20 407.38 @E
[0.03384 0.00000 0.00000 0.03384 239.71319 401.85286] @tm
0 J 2 j [] 0 d 0 R 0 @G
0.00 0.00 0.00 1.00 K
0 0.072 0.072 0.000 @w
r

% FontChange:/_Times-Roman 235.00 z
%CHAR: 0 0 (O) @t
85 157 m
104 157 123 149 136 134 c
151 119 160 98 160 77 c
160 55 151 34 138 21 c
123 6 104 -2 83 -2 c
62 -2 43 6 30 19 c
17 34 9 55 9 77 c
9 100 17 121 32 136 c
45 149 64 157 85 157 c
@c
83 151 m
70 151 57 145 49 134 c
40 121 34 102 34 79 c
34 53 40 32 49 19 c
57 11 70 4 83 4 c
96 4 111 11 119 21 c
128 32 134 51 134 74 c
134 100 128 121 117 134 c
109 145 96 151 83 151 c
@c
S
%CHAR: 217 0 (\050) @t
289 -47 m
289 -49 L
277 -45 266 -36 260 -30 c
249 -19 238 -4 234 9 c
230 23 226 40 226 55 c
226 79 232 102 243 121 c
253 140 270 155 289 164 C
289 160 L
281 155 270 147 266 138 c
260 130 253 117 251 104 c
249 91 247 74 247 60 c
247 43 249 28 249 15 c
251 6 255 -4 257 -9 c
260 -15 264 -23 268 -28 c
272 -34 281 -40 289 -45 C
289 -47 L
@c
S
%CHAR: 342 0 (5) @t
442 155 m
431 136 L
385 136 L
374 115 L
395 113 414 104 425 91 c
433 83 440 68 440 55 c
440 47 438 38 436 32 C
431 26 427 19 423 15 c
419 11 410 6 406 2 C
397 0 389 -2 380 -2 c
372 -2 363 0 361 2 C
357 4 355 9 355 11 c
355 13 357 17 357 17 c
357 17 361 19 363 19 c
363 19 368 19 368 19 c
368 19 372 15 376 15 C
380 11 387 9 391 9 c
399 9 408 13 414 17 c
421 23 425 34 425 43 c
425 51 421 62 416 68 c
410 77 399 85 391 89 c
382 91 370 96 357 96 C
387 155 L
442 155 L
@c
S
%CHAR: 507 0 (\051) @t
513 160 m
513 164 L
524 157 535 149 541 143 c
552 132 562 117 567 104 c
573 89 577 72 577 57 c
577 34 571 11 560 -9 c
547 -28 530 -43 513 -49 C
513 -45 L
522 -40 530 -34 535 -26 c
541 -17 547 -4 550 9 c
554 21 556 38 556 53 c
556 70 554 85 552 98 c
550 106 545 117 543 121 c
541 128 537 136 533 140 c
528 147 520 155 513 160 C
@c
S
T
@rax 287.71 400.18 311.69 407.38 @E
[0.03384 0.00000 0.00000 0.03384 287.73215 401.85286] @tm
 0 O 0 @g
0.00 0.00 0.00 1.00 k
e

% FontChange:/_Times-Roman 235.00 z
%CHAR: 0 0 (S) @t
106 157 m
106 104 L
102 104 L
102 113 98 123 96 128 c
91 134 85 140 81 145 c
74 149 66 151 62 151 c
53 151 47 149 43 145 c
38 140 36 132 36 126 c
36 121 38 117 40 113 C
45 109 55 98 70 89 c
83 81 94 74 98 72 C
102 68 109 62 111 57 C
115 53 117 45 117 38 c
117 28 113 17 104 11 c
96 2 81 -2 68 -2 c
64 -2 60 -2 55 -2 c
53 -2 47 0 40 2 c
34 4 28 6 28 6 C
26 6 23 4 23 4 c
23 4 21 0 21 -2 C
17 -2 L
17 51 L
21 51 L
21 38 26 28 28 23 c
30 19 36 13 43 11 C
49 6 57 4 64 4 c
72 4 83 6 87 11 c
91 15 96 23 96 30 c
96 32 94 36 94 40 C
91 45 87 49 83 51 C
81 53 72 60 60 66 c
47 72 36 79 32 83 c
28 87 21 94 19 98 c
17 102 15 111 15 115 c
15 126 19 136 26 145 c
34 153 47 157 60 157 c
66 157 77 155 85 151 C
89 151 94 149 94 149 c
94 149 98 149 98 149 c
98 149 102 153 102 157 C
106 157 L
@c
F
%CHAR: 131 0 (U) @t
242 151 m
242 155 L
295 155 L
295 151 L
288 151 l
284 151 278 147 276 143 C
276 140 274 134 274 128 c
274 64 l
274 49 271 36 269 28 c
265 19 259 11 250 6 c
242 2 229 -2 214 -2 c
197 -2 184 2 176 6 c
167 11 161 21 157 30 C
157 34 154 49 154 66 c
154 128 l
154 136 152 143 150 145 C
148 149 142 151 137 151 c
133 151 L
133 155 L
197 155 L
197 151 L
191 151 l
186 151 180 149 178 145 C
178 143 176 136 176 128 c
176 57 l
176 53 176 45 176 38 c
176 30 180 23 182 19 c
184 15 191 11 195 9 c
199 6 208 4 214 4 c
222 4 233 6 240 11 c
248 15 254 21 257 28 C
261 34 263 47 263 64 c
263 128 l
263 136 261 143 259 145 C
257 149 250 151 246 151 c
242 151 L
@c
F
%CHAR: 348 0 (\050) @t
420 -47 m
420 -49 L
408 -45 397 -36 391 -30 c
380 -19 369 -4 365 9 c
361 23 357 40 357 55 c
357 79 363 102 374 121 c
384 140 401 155 420 164 C
420 160 L
412 155 401 147 397 138 c
391 130 384 117 382 104 c
380 91 378 74 378 60 c
378 43 380 28 380 15 c
382 6 386 -4 388 -9 c
391 -15 395 -23 399 -28 c
403 -34 412 -40 420 -45 C
420 -47 L
@c
F
%CHAR: 473 0 (3) @t
484 126 m
488 134 494 145 499 149 c
505 153 516 157 524 157 c
537 157 547 153 554 145 c
558 140 562 132 562 126 c
562 115 554 102 539 91 C
547 87 556 81 560 74 c
564 68 569 60 569 51 c
569 38 564 23 556 15 c
545 4 528 -2 507 -2 c
499 -2 490 0 488 0 C
484 2 482 6 482 9 C
482 9 484 13 484 13 C
484 15 488 17 490 17 c
490 17 494 15 494 15 c
494 15 499 13 503 13 C
507 11 513 9 513 9 c
516 9 520 9 522 9 c
530 9 539 13 543 17 c
547 21 552 30 552 36 c
552 43 550 51 547 55 C
545 57 541 62 541 64 C
537 66 533 70 528 72 c
524 74 516 77 511 77 c
507 77 L
507 81 L
511 81 520 83 524 85 c
530 89 537 94 539 98 C
543 102 545 111 545 115 c
545 123 541 130 537 134 c
533 138 524 143 518 143 c
505 143 494 136 486 123 C
484 126 L
@c
F
%CHAR: 638 0 (\051) @t
644 160 m
644 164 L
655 157 666 149 672 143 c
683 132 693 117 698 104 c
704 89 708 72 708 57 c
708 34 702 11 691 -9 c
678 -28 661 -43 644 -49 C
644 -45 L
653 -40 661 -34 666 -26 c
672 -17 678 -4 681 9 c
685 21 687 38 687 53 c
687 70 685 85 683 98 c
681 106 676 117 674 121 c
672 128 668 136 664 140 c
659 147 651 155 644 160 C
@c
F
T
@rax 287.71 400.18 311.69 407.38 @E
[0.03384 0.00000 0.00000 0.03384 287.73215 401.85286] @tm
0 J 2 j [] 0 d 0 R 0 @G
0.00 0.00 0.00 1.00 K
0 0.072 0.072 0.000 @w
r

% FontChange:/_Times-Roman 235.00 z
%CHAR: 0 0 (S) @t
106 157 m
106 104 L
102 104 L
102 113 98 123 96 128 c
91 134 85 140 81 145 c
74 149 66 151 62 151 c
53 151 47 149 43 145 c
38 140 36 132 36 126 c
36 121 38 117 40 113 C
45 109 55 98 70 89 c
83 81 94 74 98 72 C
102 68 109 62 111 57 C
115 53 117 45 117 38 c
117 28 113 17 104 11 c
96 2 81 -2 68 -2 c
64 -2 60 -2 55 -2 c
53 -2 47 0 40 2 c
34 4 28 6 28 6 C
26 6 23 4 23 4 c
23 4 21 0 21 -2 C
17 -2 L
17 51 L
21 51 L
21 38 26 28 28 23 c
30 19 36 13 43 11 C
49 6 57 4 64 4 c
72 4 83 6 87 11 c
91 15 96 23 96 30 c
96 32 94 36 94 40 C
91 45 87 49 83 51 C
81 53 72 60 60 66 c
47 72 36 79 32 83 c
28 87 21 94 19 98 c
17 102 15 111 15 115 c
15 126 19 136 26 145 c
34 153 47 157 60 157 c
66 157 77 155 85 151 C
89 151 94 149 94 149 c
94 149 98 149 98 149 c
98 149 102 153 102 157 C
106 157 L
@c
S
%CHAR: 131 0 (U) @t
242 151 m
242 155 L
295 155 L
295 151 L
288 151 l
284 151 278 147 276 143 C
276 140 274 134 274 128 c
274 64 l
274 49 271 36 269 28 c
265 19 259 11 250 6 c
242 2 229 -2 214 -2 c
197 -2 184 2 176 6 c
167 11 161 21 157 30 C
157 34 154 49 154 66 c
154 128 l
154 136 152 143 150 145 C
148 149 142 151 137 151 c
133 151 L
133 155 L
197 155 L
197 151 L
191 151 l
186 151 180 149 178 145 C
178 143 176 136 176 128 c
176 57 l
176 53 176 45 176 38 c
176 30 180 23 182 19 c
184 15 191 11 195 9 c
199 6 208 4 214 4 c
222 4 233 6 240 11 c
248 15 254 21 257 28 C
261 34 263 47 263 64 c
263 128 l
263 136 261 143 259 145 C
257 149 250 151 246 151 c
242 151 L
@c
S
%CHAR: 348 0 (\050) @t
420 -47 m
420 -49 L
408 -45 397 -36 391 -30 c
380 -19 369 -4 365 9 c
361 23 357 40 357 55 c
357 79 363 102 374 121 c
384 140 401 155 420 164 C
420 160 L
412 155 401 147 397 138 c
391 130 384 117 382 104 c
380 91 378 74 378 60 c
378 43 380 28 380 15 c
382 6 386 -4 388 -9 c
391 -15 395 -23 399 -28 c
403 -34 412 -40 420 -45 C
420 -47 L
@c
S
%CHAR: 473 0 (3) @t
484 126 m
488 134 494 145 499 149 c
505 153 516 157 524 157 c
537 157 547 153 554 145 c
558 140 562 132 562 126 c
562 115 554 102 539 91 C
547 87 556 81 560 74 c
564 68 569 60 569 51 c
569 38 564 23 556 15 c
545 4 528 -2 507 -2 c
499 -2 490 0 488 0 C
484 2 482 6 482 9 C
482 9 484 13 484 13 C
484 15 488 17 490 17 c
490 17 494 15 494 15 c
494 15 499 13 503 13 C
507 11 513 9 513 9 c
516 9 520 9 522 9 c
530 9 539 13 543 17 c
547 21 552 30 552 36 c
552 43 550 51 547 55 C
545 57 541 62 541 64 C
537 66 533 70 528 72 c
524 74 516 77 511 77 c
507 77 L
507 81 L
511 81 520 83 524 85 c
530 89 537 94 539 98 C
543 102 545 111 545 115 c
545 123 541 130 537 134 c
533 138 524 143 518 143 c
505 143 494 136 486 123 C
484 126 L
@c
S
%CHAR: 638 0 (\051) @t
644 160 m
644 164 L
655 157 666 149 672 143 c
683 132 693 117 698 104 c
704 89 708 72 708 57 c
708 34 702 11 691 -9 c
678 -28 661 -43 644 -49 C
644 -45 L
653 -40 661 -34 666 -26 c
672 -17 678 -4 681 9 c
685 21 687 38 687 53 c
687 70 685 85 683 98 c
681 106 676 117 674 121 c
672 128 668 136 664 140 c
659 147 651 155 644 160 C
@c
S
T
@rax 240.19 346.39 259.70 353.66 @E
[0.03384 0.00000 0.00000 0.03384 240.18695 348.11495] @tm
 0 O 0 @g
0.00 0.00 0.00 1.00 k
e

% FontChange:/_Times-Roman 235.00 z
%CHAR: 0 0 (O) @t
85 157 m
104 157 123 149 136 134 c
151 119 160 98 160 77 c
160 55 151 34 138 21 c
123 6 104 -2 83 -2 c
62 -2 43 6 30 19 c
17 34 9 55 9 77 c
9 100 17 121 32 136 c
45 149 64 157 85 157 c
@c
83 151 m
70 151 57 145 49 134 c
40 121 34 102 34 79 c
34 53 40 32 49 19 c
57 11 70 4 83 4 c
96 4 111 11 119 21 c
128 32 134 51 134 74 c
134 100 128 121 117 134 c
109 145 96 151 83 151 c
@c
F
%CHAR: 217 0 (\050) @t
289 -47 m
289 -49 L
277 -45 266 -36 260 -30 c
249 -19 238 -4 234 9 c
230 23 226 40 226 55 c
226 79 232 102 243 121 c
253 140 270 155 289 164 C
289 160 L
281 155 270 147 266 138 c
260 130 253 117 251 104 c
249 91 247 74 247 60 c
247 43 249 28 249 15 c
251 6 255 -4 257 -9 c
260 -15 264 -23 268 -28 c
272 -34 281 -40 289 -45 C
289 -47 L
@c
F
%CHAR: 342 0 (3) @t
353 126 m
357 134 363 145 368 149 c
374 153 385 157 393 157 c
406 157 416 153 423 145 c
427 140 431 132 431 126 c
431 115 423 102 408 91 C
416 87 425 81 429 74 c
433 68 438 60 438 51 c
438 38 433 23 425 15 c
414 4 397 -2 376 -2 c
368 -2 359 0 357 0 C
353 2 351 6 351 9 C
351 9 353 13 353 13 C
353 15 357 17 359 17 c
359 17 363 15 363 15 c
363 15 368 13 372 13 C
376 11 382 9 382 9 c
385 9 389 9 391 9 c
399 9 408 13 412 17 c
416 21 421 30 421 36 c
421 43 419 51 416 55 C
414 57 410 62 410 64 C
406 66 402 70 397 72 c
393 74 385 77 380 77 c
376 77 L
376 81 L
380 81 389 83 393 85 c
399 89 406 94 408 98 C
412 102 414 111 414 115 c
414 123 410 130 406 134 c
402 138 393 143 387 143 c
374 143 363 136 355 123 C
353 126 L
@c
F
%CHAR: 507 0 (\051) @t
513 160 m
513 164 L
524 157 535 149 541 143 c
552 132 562 117 567 104 c
573 89 577 72 577 57 c
577 34 571 11 560 -9 c
547 -28 530 -43 513 -49 C
513 -45 L
522 -40 530 -34 535 -26 c
541 -17 547 -4 550 9 c
554 21 556 38 556 53 c
556 70 554 85 552 98 c
550 106 545 117 543 121 c
541 128 537 136 533 140 c
528 147 520 155 513 160 C
@c
F
T
@rax 240.19 346.39 259.70 353.66 @E
[0.03384 0.00000 0.00000 0.03384 240.18695 348.11495] @tm
0 J 2 j [] 0 d 0 R 0 @G
0.00 0.00 0.00 1.00 K
0 0.072 0.072 0.000 @w
r

% FontChange:/_Times-Roman 235.00 z
%CHAR: 0 0 (O) @t
85 157 m
104 157 123 149 136 134 c
151 119 160 98 160 77 c
160 55 151 34 138 21 c
123 6 104 -2 83 -2 c
62 -2 43 6 30 19 c
17 34 9 55 9 77 c
9 100 17 121 32 136 c
45 149 64 157 85 157 c
@c
83 151 m
70 151 57 145 49 134 c
40 121 34 102 34 79 c
34 53 40 32 49 19 c
57 11 70 4 83 4 c
96 4 111 11 119 21 c
128 32 134 51 134 74 c
134 100 128 121 117 134 c
109 145 96 151 83 151 c
@c
S
%CHAR: 217 0 (\050) @t
289 -47 m
289 -49 L
277 -45 266 -36 260 -30 c
249 -19 238 -4 234 9 c
230 23 226 40 226 55 c
226 79 232 102 243 121 c
253 140 270 155 289 164 C
289 160 L
281 155 270 147 266 138 c
260 130 253 117 251 104 c
249 91 247 74 247 60 c
247 43 249 28 249 15 c
251 6 255 -4 257 -9 c
260 -15 264 -23 268 -28 c
272 -34 281 -40 289 -45 C
289 -47 L
@c
S
%CHAR: 342 0 (3) @t
353 126 m
357 134 363 145 368 149 c
374 153 385 157 393 157 c
406 157 416 153 423 145 c
427 140 431 132 431 126 c
431 115 423 102 408 91 C
416 87 425 81 429 74 c
433 68 438 60 438 51 c
438 38 433 23 425 15 c
414 4 397 -2 376 -2 c
368 -2 359 0 357 0 C
353 2 351 6 351 9 C
351 9 353 13 353 13 C
353 15 357 17 359 17 c
359 17 363 15 363 15 c
363 15 368 13 372 13 C
376 11 382 9 382 9 c
385 9 389 9 391 9 c
399 9 408 13 412 17 c
416 21 421 30 421 36 c
421 43 419 51 416 55 C
414 57 410 62 410 64 C
406 66 402 70 397 72 c
393 74 385 77 380 77 c
376 77 L
376 81 L
380 81 389 83 393 85 c
399 89 406 94 408 98 C
412 102 414 111 414 115 c
414 123 410 130 406 134 c
402 138 393 143 387 143 c
374 143 363 136 355 123 C
353 126 L
@c
S
%CHAR: 507 0 (\051) @t
513 160 m
513 164 L
524 157 535 149 541 143 c
552 132 562 117 567 104 c
573 89 577 72 577 57 c
577 34 571 11 560 -9 c
547 -28 530 -43 513 -49 C
513 -45 L
522 -40 530 -34 535 -26 c
541 -17 547 -4 550 9 c
554 21 556 38 556 53 c
556 70 554 85 552 98 c
550 106 545 117 543 121 c
541 128 537 136 533 140 c
528 147 520 155 513 160 C
@c
S
T
@rax 193.39 346.39 212.90 353.66 @E
[0.03384 0.00000 0.00000 0.03384 193.42007 348.11495] @tm
 0 O 0 @g
0.00 0.00 0.00 1.00 k
e

% FontChange:/_Times-Roman 235.00 z
%CHAR: 0 0 (O) @t
85 157 m
104 157 123 149 136 134 c
151 119 160 98 160 77 c
160 55 151 34 138 21 c
123 6 104 -2 83 -2 c
62 -2 43 6 30 19 c
17 34 9 55 9 77 c
9 100 17 121 32 136 c
45 149 64 157 85 157 c
@c
83 151 m
70 151 57 145 49 134 c
40 121 34 102 34 79 c
34 53 40 32 49 19 c
57 11 70 4 83 4 c
96 4 111 11 119 21 c
128 32 134 51 134 74 c
134 100 128 121 117 134 c
109 145 96 151 83 151 c
@c
F
%CHAR: 217 0 (\050) @t
289 -47 m
289 -49 L
277 -45 266 -36 260 -30 c
249 -19 238 -4 234 9 c
230 23 226 40 226 55 c
226 79 232 102 243 121 c
253 140 270 155 289 164 C
289 160 L
281 155 270 147 266 138 c
260 130 253 117 251 104 c
249 91 247 74 247 60 c
247 43 249 28 249 15 c
251 6 255 -4 257 -9 c
260 -15 264 -23 268 -28 c
272 -34 281 -40 289 -45 C
289 -47 L
@c
F
%CHAR: 342 0 (3) @t
353 126 m
357 134 363 145 368 149 c
374 153 385 157 393 157 c
406 157 416 153 423 145 c
427 140 431 132 431 126 c
431 115 423 102 408 91 C
416 87 425 81 429 74 c
433 68 438 60 438 51 c
438 38 433 23 425 15 c
414 4 397 -2 376 -2 c
368 -2 359 0 357 0 C
353 2 351 6 351 9 C
351 9 353 13 353 13 C
353 15 357 17 359 17 c
359 17 363 15 363 15 c
363 15 368 13 372 13 C
376 11 382 9 382 9 c
385 9 389 9 391 9 c
399 9 408 13 412 17 c
416 21 421 30 421 36 c
421 43 419 51 416 55 C
414 57 410 62 410 64 C
406 66 402 70 397 72 c
393 74 385 77 380 77 c
376 77 L
376 81 L
380 81 389 83 393 85 c
399 89 406 94 408 98 C
412 102 414 111 414 115 c
414 123 410 130 406 134 c
402 138 393 143 387 143 c
374 143 363 136 355 123 C
353 126 L
@c
F
%CHAR: 507 0 (\051) @t
513 160 m
513 164 L
524 157 535 149 541 143 c
552 132 562 117 567 104 c
573 89 577 72 577 57 c
577 34 571 11 560 -9 c
547 -28 530 -43 513 -49 C
513 -45 L
522 -40 530 -34 535 -26 c
541 -17 547 -4 550 9 c
554 21 556 38 556 53 c
556 70 554 85 552 98 c
550 106 545 117 543 121 c
541 128 537 136 533 140 c
528 147 520 155 513 160 C
@c
F
T
@rax 193.39 346.39 212.90 353.66 @E
[0.03384 0.00000 0.00000 0.03384 193.42007 348.11495] @tm
0 J 2 j [] 0 d 0 R 0 @G
0.00 0.00 0.00 1.00 K
0 0.072 0.072 0.000 @w
r

% FontChange:/_Times-Roman 235.00 z
%CHAR: 0 0 (O) @t
85 157 m
104 157 123 149 136 134 c
151 119 160 98 160 77 c
160 55 151 34 138 21 c
123 6 104 -2 83 -2 c
62 -2 43 6 30 19 c
17 34 9 55 9 77 c
9 100 17 121 32 136 c
45 149 64 157 85 157 c
@c
83 151 m
70 151 57 145 49 134 c
40 121 34 102 34 79 c
34 53 40 32 49 19 c
57 11 70 4 83 4 c
96 4 111 11 119 21 c
128 32 134 51 134 74 c
134 100 128 121 117 134 c
109 145 96 151 83 151 c
@c
S
%CHAR: 217 0 (\050) @t
289 -47 m
289 -49 L
277 -45 266 -36 260 -30 c
249 -19 238 -4 234 9 c
230 23 226 40 226 55 c
226 79 232 102 243 121 c
253 140 270 155 289 164 C
289 160 L
281 155 270 147 266 138 c
260 130 253 117 251 104 c
249 91 247 74 247 60 c
247 43 249 28 249 15 c
251 6 255 -4 257 -9 c
260 -15 264 -23 268 -28 c
272 -34 281 -40 289 -45 C
289 -47 L
@c
S
%CHAR: 342 0 (3) @t
353 126 m
357 134 363 145 368 149 c
374 153 385 157 393 157 c
406 157 416 153 423 145 c
427 140 431 132 431 126 c
431 115 423 102 408 91 C
416 87 425 81 429 74 c
433 68 438 60 438 51 c
438 38 433 23 425 15 c
414 4 397 -2 376 -2 c
368 -2 359 0 357 0 C
353 2 351 6 351 9 C
351 9 353 13 353 13 C
353 15 357 17 359 17 c
359 17 363 15 363 15 c
363 15 368 13 372 13 C
376 11 382 9 382 9 c
385 9 389 9 391 9 c
399 9 408 13 412 17 c
416 21 421 30 421 36 c
421 43 419 51 416 55 C
414 57 410 62 410 64 C
406 66 402 70 397 72 c
393 74 385 77 380 77 c
376 77 L
376 81 L
380 81 389 83 393 85 c
399 89 406 94 408 98 C
412 102 414 111 414 115 c
414 123 410 130 406 134 c
402 138 393 143 387 143 c
374 143 363 136 355 123 C
353 126 L
@c
S
%CHAR: 507 0 (\051) @t
513 160 m
513 164 L
524 157 535 149 541 143 c
552 132 562 117 567 104 c
573 89 577 72 577 57 c
577 34 571 11 560 -9 c
547 -28 530 -43 513 -49 C
513 -45 L
522 -40 530 -34 535 -26 c
541 -17 547 -4 550 9 c
554 21 556 38 556 53 c
556 70 554 85 552 98 c
550 106 545 117 543 121 c
541 128 537 136 533 140 c
528 147 520 155 513 160 C
@c
S
T
@rax 285.98 346.39 305.50 353.66 @E
[0.03384 0.00000 0.00000 0.03384 286.00631 348.11495] @tm
 0 O 0 @g
0.00 0.00 0.00 1.00 k
e

% FontChange:/_Times-Roman 235.00 z
%CHAR: 0 0 (O) @t
85 157 m
104 157 123 149 136 134 c
151 119 160 98 160 77 c
160 55 151 34 138 21 c
123 6 104 -2 83 -2 c
62 -2 43 6 30 19 c
17 34 9 55 9 77 c
9 100 17 121 32 136 c
45 149 64 157 85 157 c
@c
83 151 m
70 151 57 145 49 134 c
40 121 34 102 34 79 c
34 53 40 32 49 19 c
57 11 70 4 83 4 c
96 4 111 11 119 21 c
128 32 134 51 134 74 c
134 100 128 121 117 134 c
109 145 96 151 83 151 c
@c
F
%CHAR: 217 0 (\050) @t
289 -47 m
289 -49 L
277 -45 266 -36 260 -30 c
249 -19 238 -4 234 9 c
230 23 226 40 226 55 c
226 79 232 102 243 121 c
253 140 270 155 289 164 C
289 160 L
281 155 270 147 266 138 c
260 130 253 117 251 104 c
249 91 247 74 247 60 c
247 43 249 28 249 15 c
251 6 255 -4 257 -9 c
260 -15 264 -23 268 -28 c
272 -34 281 -40 289 -45 C
289 -47 L
@c
F
%CHAR: 342 0 (3) @t
353 126 m
357 134 363 145 368 149 c
374 153 385 157 393 157 c
406 157 416 153 423 145 c
427 140 431 132 431 126 c
431 115 423 102 408 91 C
416 87 425 81 429 74 c
433 68 438 60 438 51 c
438 38 433 23 425 15 c
414 4 397 -2 376 -2 c
368 -2 359 0 357 0 C
353 2 351 6 351 9 C
351 9 353 13 353 13 C
353 15 357 17 359 17 c
359 17 363 15 363 15 c
363 15 368 13 372 13 C
376 11 382 9 382 9 c
385 9 389 9 391 9 c
399 9 408 13 412 17 c
416 21 421 30 421 36 c
421 43 419 51 416 55 C
414 57 410 62 410 64 C
406 66 402 70 397 72 c
393 74 385 77 380 77 c
376 77 L
376 81 L
380 81 389 83 393 85 c
399 89 406 94 408 98 C
412 102 414 111 414 115 c
414 123 410 130 406 134 c
402 138 393 143 387 143 c
374 143 363 136 355 123 C
353 126 L
@c
F
%CHAR: 507 0 (\051) @t
513 160 m
513 164 L
524 157 535 149 541 143 c
552 132 562 117 567 104 c
573 89 577 72 577 57 c
577 34 571 11 560 -9 c
547 -28 530 -43 513 -49 C
513 -45 L
522 -40 530 -34 535 -26 c
541 -17 547 -4 550 9 c
554 21 556 38 556 53 c
556 70 554 85 552 98 c
550 106 545 117 543 121 c
541 128 537 136 533 140 c
528 147 520 155 513 160 C
@c
F
T
@rax 285.98 346.39 305.50 353.66 @E
[0.03384 0.00000 0.00000 0.03384 286.00631 348.11495] @tm
0 J 2 j [] 0 d 0 R 0 @G
0.00 0.00 0.00 1.00 K
0 0.072 0.072 0.000 @w
r

% FontChange:/_Times-Roman 235.00 z
%CHAR: 0 0 (O) @t
85 157 m
104 157 123 149 136 134 c
151 119 160 98 160 77 c
160 55 151 34 138 21 c
123 6 104 -2 83 -2 c
62 -2 43 6 30 19 c
17 34 9 55 9 77 c
9 100 17 121 32 136 c
45 149 64 157 85 157 c
@c
83 151 m
70 151 57 145 49 134 c
40 121 34 102 34 79 c
34 53 40 32 49 19 c
57 11 70 4 83 4 c
96 4 111 11 119 21 c
128 32 134 51 134 74 c
134 100 128 121 117 134 c
109 145 96 151 83 151 c
@c
S
%CHAR: 217 0 (\050) @t
289 -47 m
289 -49 L
277 -45 266 -36 260 -30 c
249 -19 238 -4 234 9 c
230 23 226 40 226 55 c
226 79 232 102 243 121 c
253 140 270 155 289 164 C
289 160 L
281 155 270 147 266 138 c
260 130 253 117 251 104 c
249 91 247 74 247 60 c
247 43 249 28 249 15 c
251 6 255 -4 257 -9 c
260 -15 264 -23 268 -28 c
272 -34 281 -40 289 -45 C
289 -47 L
@c
S
%CHAR: 342 0 (3) @t
353 126 m
357 134 363 145 368 149 c
374 153 385 157 393 157 c
406 157 416 153 423 145 c
427 140 431 132 431 126 c
431 115 423 102 408 91 C
416 87 425 81 429 74 c
433 68 438 60 438 51 c
438 38 433 23 425 15 c
414 4 397 -2 376 -2 c
368 -2 359 0 357 0 C
353 2 351 6 351 9 C
351 9 353 13 353 13 C
353 15 357 17 359 17 c
359 17 363 15 363 15 c
363 15 368 13 372 13 C
376 11 382 9 382 9 c
385 9 389 9 391 9 c
399 9 408 13 412 17 c
416 21 421 30 421 36 c
421 43 419 51 416 55 C
414 57 410 62 410 64 C
406 66 402 70 397 72 c
393 74 385 77 380 77 c
376 77 L
376 81 L
380 81 389 83 393 85 c
399 89 406 94 408 98 C
412 102 414 111 414 115 c
414 123 410 130 406 134 c
402 138 393 143 387 143 c
374 143 363 136 355 123 C
353 126 L
@c
S
%CHAR: 507 0 (\051) @t
513 160 m
513 164 L
524 157 535 149 541 143 c
552 132 562 117 567 104 c
573 89 577 72 577 57 c
577 34 571 11 560 -9 c
547 -28 530 -43 513 -49 C
513 -45 L
522 -40 530 -34 535 -26 c
541 -17 547 -4 550 9 c
554 21 556 38 556 53 c
556 70 554 85 552 98 c
550 106 545 117 543 121 c
541 128 537 136 533 140 c
528 147 520 155 513 160 C
@c
S
T
@rax %Note: Object
299.38 417.02 299.66 468.58 @E
0 J 0 j [] 0 d 0 R 0 @G
0.00 0.00 0.00 1.00 K
0 1.440 1.440 0.000 @w
299.52 468.58 m
299.52 420.41 L
S
@j
0.00 0.00 0.00 1.00 K
0.00 0.00 0.00 1.00 k
0 @g
0 @G
[] 0 d 0 J 0 j
0 R 0 O
0 1.44 1.44 0 @w
303.05 425.09 m
299.52 416.95 L
295.99 425.09 L
303.05 425.09 L
f
@J

@rax %Note: Object
199.30 416.16 199.58 435.96 @E
0 J 0 j [] 0 d 0 R 0 @G
0.00 0.00 0.00 1.00 K
0 1.440 1.440 0.000 @w
199.44 435.96 m
199.44 419.54 L
S
@j
0.00 0.00 0.00 1.00 K
0.00 0.00 0.00 1.00 k
0 @g
0 @G
[] 0 d 0 J 0 j
0 R 0 O
0 1.44 1.44 0 @w
202.97 424.22 m
199.44 416.09 L
195.91 424.22 L
202.97 424.22 L
f
@J

@rax %Note: Object
199.30 368.93 199.58 388.66 @E
0 J 0 j [] 0 d 0 R 0 @G
0.00 0.00 0.00 1.00 K
0 1.440 1.440 0.000 @w
199.44 388.66 m
199.44 372.31 L
S
@j
0.00 0.00 0.00 1.00 K
0.00 0.00 0.00 1.00 k
0 @g
0 @G
[] 0 d 0 J 0 j
0 R 0 O
0 1.44 1.44 0 @w
202.97 376.99 m
199.44 368.86 L
195.91 376.99 L
202.97 376.99 L
f
@J

@rax %Note: Object
249.48 368.93 249.77 388.66 @E
0 J 0 j [] 0 d 0 R 0 @G
0.00 0.00 0.00 1.00 K
0 1.440 1.440 0.000 @w
249.62 388.66 m
249.62 372.31 L
S
@j
0.00 0.00 0.00 1.00 K
0.00 0.00 0.00 1.00 k
0 @g
0 @G
[] 0 d 0 J 0 j
0 R 0 O
0 1.44 1.44 0 @w
253.15 376.99 m
249.62 368.86 L
246.10 376.99 L
253.15 376.99 L
f
@J

@rax %Note: Object
249.48 468.58 249.77 488.30 @E
0 J 0 j [] 0 d 0 R 0 @G
0.00 0.00 0.00 1.00 K
0 1.440 1.440 0.000 @w
249.62 488.30 m
249.62 471.96 L
S
@j
0.00 0.00 0.00 1.00 K
0.00 0.00 0.00 1.00 k
0 @g
0 @G
[] 0 d 0 J 0 j
0 R 0 O
0 1.44 1.44 0 @w
253.15 476.64 m
249.62 468.50 L
246.10 476.64 L
253.15 476.64 L
f
@J

@rax %Note: Object
249.48 416.16 249.77 435.96 @E
0 J 0 j [] 0 d 0 R 0 @G
0.00 0.00 0.00 1.00 K
0 1.440 1.440 0.000 @w
249.62 435.96 m
249.62 419.54 L
S
@j
0.00 0.00 0.00 1.00 K
0.00 0.00 0.00 1.00 k
0 @g
0 @G
[] 0 d 0 J 0 j
0 R 0 O
0 1.44 1.44 0 @w
253.15 424.22 m
249.62 416.09 L
246.10 424.22 L
253.15 424.22 L
f
@J

@rax %Note: Object
299.38 368.93 299.66 388.66 @E
0 J 0 j [] 0 d 0 R 0 @G
0.00 0.00 0.00 1.00 K
0 1.440 1.440 0.000 @w
299.52 388.66 m
299.52 372.31 L
S
@j
0.00 0.00 0.00 1.00 K
0.00 0.00 0.00 1.00 k
0 @g
0 @G
[] 0 d 0 J 0 j
0 R 0 O
0 1.44 1.44 0 @w
303.05 376.99 m
299.52 368.86 L
295.99 376.99 L
303.05 376.99 L
f
@J

@rax %Note: Object
199.44 468.58 229.39 488.30 @E
0 J 0 j [] 0 d 0 R 0 @G
0.00 0.00 0.00 1.00 K
0 1.440 1.440 0.000 @w
229.39 488.30 m
202.10 470.30 L
S
@j
0.00 0.00 0.00 1.00 K
0.00 0.00 0.00 1.00 k
0 @g
0 @G
[] 0 d 0 J 0 j
0 R 0 O
0 1.37 1.37 0 @w
207.65 470.02 m
199.37 468.50 L
203.98 475.56 L
207.65 470.02 L
f
@J

@rax %Note: Object
269.50 468.58 299.52 488.30 @E
0 J 0 j [] 0 d 0 R 0 @G
0.00 0.00 0.00 1.00 K
0 1.440 1.440 0.000 @w
269.50 488.30 m
299.52 468.58 L
S

@rs @rs @sm
%EndColorLayer
@rs
@rs
%EndPage
%%Trailer
@EndSysCorelDict
end
%%DocumentSuppliedResources: procset wCorel5Dict
