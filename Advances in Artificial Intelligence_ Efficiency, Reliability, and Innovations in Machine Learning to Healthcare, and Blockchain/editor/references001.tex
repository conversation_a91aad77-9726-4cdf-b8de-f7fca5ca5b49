%
% ---- Bibliography ----
%
% BibTeX users should specify bibliography style 'splncs04'.
% References will then be sorted and formatted in the correct style.
%
% \bibliographystyle{splncs04}
% \bibliography{mybibliography}
%
\begin{thebibliography}{8}
\bibitem{novikov2015tensorizing}
<PERSON>, A<PERSON>, <PERSON>, D<PERSON>, <PERSON>, A. and <PERSON>, D. P.: Tensorizing neural networks, In \textit{Advances in Neural Information Processing Systems,} Vol. 28 (2015).

\bibitem{novikov2021tensor}
<PERSON>kov, G. S., Panov, M. E. and Oseledets, I. V.: Tensor-train density estimation, In \textit{Proceedings of Conference on Uncertainty in Artificial Intelligence,} pp. 1321–1331 (2021).

\bibitem{oseledets2011tensor}
Oseledets, I. V.: Tensor-Train decomposition, \textit{SIAM Journal on Scientific Computing,} Vol. 33, No. 5, pp. 2295–2317 (2011).

\bibitem{polson2013bayesian}
<PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON>, <PERSON> and <PERSON>, J.: Bayesian inference for logistic models using P\'{o}lya–Gamma latent variables, \textit{Journal of the American Statistical Association,} Vol. 108, No. 504, pp. 1339–1349 (2013).

\bibitem{rieser2023tensor}
Rieser, H.-M., K¨oster, F. and Raulf, A. P.: Tensor networks for quantum machine learning, \textit{Proceedings of the Royal Society A,} Vol. 479, No. 2275, p. 20230218 (2023).

\bibitem{scott2013expectation}
Scott, J. G. and Sun, L.: Expectation-maximization for logistic regression, \textit{arXiv preprint arXiv:1306.0040} (2013).

\bibitem{stoudenmire2016supervised}
Stoudenmire, E. and Schwab, D. J.: Supervised learning with tensor networks, \textit{Advances in Neural Information Processing Systems,} Vol. 29 (2016).

\bibitem{sun2016majorization}
Sun, Y., Babu, P. and Palomar, D. P.: Majorization-minimization algorithms in signal processing, communications, and machine learning, \textit{IEEE Transactions on Signal Processing,} Vol. 65, No. 3, pp. 794–816 (2016).

\bibitem{wall2022tensor}
Wall, M. L., Titum, P., Quiroz, G., Foss-Feig, M. and Hazzard, K. R.: Tensor-network discriminator architecture for classification of quantum data on quantum computers, \textit{Physical Review A,} Vol. 105, No. 6, p. 062439 (2022).

\bibitem{wang2023tensor}
Wang, M., Pan, Y., Yang, X., Li, G. and Xu, Z.: Tensor networks meet neural networks: A survey, \textit{arXiv preprint arXiv:2302.09019} (2023).


\end{thebibliography}
