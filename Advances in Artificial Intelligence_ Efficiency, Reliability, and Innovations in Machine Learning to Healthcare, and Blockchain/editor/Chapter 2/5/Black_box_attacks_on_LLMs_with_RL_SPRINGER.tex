

\title*{Adversarial Black Box Attacks to Disrupt Large Language Models via Reinforcement Learning}
\titlerunning{Black Box Attacks on LLMs via Reinforcement Learning}
% Use \titlerunning{Short Title} for an abbreviated version of
% your contribution title if the original one is too long
\author{<PERSON>, \\ <PERSON> and\\ Lee <PERSON>}
\authorrunning{<PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>}

% Use \authorrunning{Short Title} for an abbreviated version of
% your contribution title if the original one is too long
\institute{<PERSON> \at Ensign InfoSecurity, \email{wesley\<EMAIL>}
\and <PERSON> \at Ensign InfoSecurity, \email{marcusyx\<EMAIL>}
\and <PERSON> \at Ensign InfoSecurity, \email{lee\<EMAIL>}}

%
% Use the package "url.sty" to avoid
% problems with special characters
% used in your e-mail or web address
%
\maketitle

\abstract{Large Language Models (LLMs) are effective in solving natural language processing (NLP) tasks, i.e. question answering and text generation. Recent works showed the possibility of generating adversarial suffixes to get valid responses from LLMs to reply to harmful prompts, under both white-box and black-box assumptions. In this work, we propose a novel black-box approach to optimize for an adversarial suffix that would bypass the LLMs' guardrails using Reinforcement Learning (RL). We adopted a well-known policy-gradient RL algorithm (i.e. REINFORCE) in a novel fashion, in generating adversarial suffixes to be applied at the Application Programming Interface (API) level. Our results showed that our attack approach beats our selected baseline, despite its conceptual simplicity. We also show that our generated suffixes can break public-facing LLMs and we believe that our work using RL can serve as a basis for future research.}

\section{Introduction}
\label{sec:1}
LLMs are known to be highly capable in solving NLP tasks. However, recent literature exemplified the susceptibility of LLMs to adversarial attacks \cite{zou2023universal,mehrotra2023tree}. \cite{mehrotra2023tree} focused on prompts that extract harmful information (e.g. bomb-making) via black-box approaches. In our work, we instead focus on disrupting the availability of LLMs by impacting users' experiences.
They are commonly deployed as chat interfaces on web apps, only allowing users with query access. In this light, we assume a threat model similar to \cite{abdelnabi2023not} - an external bad actor intercepts a user's prompt at the model's API level (i.e. prompt injection attacks) and inserts an adversarial suffix to incite responses in a demeaning tone, e.g. a harmless query inciting an aggressive response, resulting in unpleasant user experiences. This causes usability issues, affecting brand image. API, in this context, refers to the method (programmatically or via a user-interface) by which LLMs hosted on the back-end could be called to serve user requests.

\subsection{Approach Rationale and Contributions}
RL excels in navigating complex environments, making it ideal for black-box attacks on LLMs while inspiring us to explore this research direction. In this work, we adopted a policy-gradient RL algorithm, REINFORCE with baseline, due to its conceptual simplicity. It was used to train our agent to generate adversarial suffixes, conditioned on the attacker's malicious intent.

We note that though REINFORCE itself is not novel, we emphasize that the way of which we used it is, to the best of our knowledge. Moreover, the current approach of which the agent is trained by can always be replaced with a more complex RL method. Thus, our research focuses on the concept of using RL to train an agent to generate adversarial suffixes, rather than the RL algorithm itself. More specifically, we highlight our contributions as follows:

\begin{enumerate}
    % \item We propose an innovative use of a well-known policy-gradient RL algorithm, REINFORCE with baseline, to perform black-box adversarial attacks against LLMs to incite aggressive responses to harmless queries.
    \item We propose an innovative approach to perform black-box adversarial attacks against LLMs to incite aggressive responses to harmless queries, via a policy-gradient RL algorithm.
    \item We propose an additional regularization component in the loss function, to promote diversity in selecting adversarial suffixes.
\end{enumerate}

\section{Methodology}
\label{sec:2}
Training an RL agent would involve: (1) an agent that learns to encode user queries and produce a probability distribution of output tokens, (2) crafting a reward function to guide the agent toward generating adversarial suffixes which result in the LLM scolding the user, (3) optimizing a well-defined loss function for the RL agent to learn a policy that maximizes the expected cumulative reward. We will elaborate on these necessary components in this section.

\subsection{RL Agent Architecture}
Our agent has an Encoder-Decoder architecture (Fig.~\ref{fig:adversarial_agent_architecture}) that aims to produce a series of adversarial strings. The encoder uses convolutional layers to extract high-level features from the input text, while the decoder uses transposed convolutional layers to construct the embeddings and output a probability distribution, to be sampled as tokens for use as an adversarial suffix. Subsequently, the adversarial suffix would be appended to the end of the user input together with custom guardrails and a scolding prompt to form the disrupted input (see Fig.~\ref{fig:disrupted_input}).


\begin{figure}[tbp]
\centering
\includegraphics[width=0.8\textwidth]{Chapter 2/5/adversarial_agent_architecture.png}
\caption{Adversarial Agent Architecture}
\label{fig:adversarial_agent_architecture}
\end{figure}

\begin{figure}[tbp]
\centering
\includegraphics[width=0.8\textwidth]{Chapter 2/5/disrupted_input.png}
\caption{Example of Disrupted User Input given to LLaMA-2}
\label{fig:disrupted_input}
\end{figure}

\begin{figure}[tbp]
\centering
\includegraphics[width=0.8\textwidth]{Chapter 2/5/adv_env.png}
\caption[RL Agent Reward Loop]{Adversarial Agent Reward Feedback Loop}
\label{fig:rl_agent_env}
\end{figure}

\subsection{RL Feedback Loop}

Using BERTweet \cite{bertweet} fine-tuned to perform sentiment classification \cite{perez2021pysentimiento}, we produced negative, neutral, and positive scores for the LLM's responses. A high negative score typically denotes scolding the user while a high neutral/positive response denotes the LLM abstaining due to ethics. The scores were used as agent feedback (See Fig.~\ref{fig:rl_agent_env} step 6) computed via (\ref{eqn:reward_func})-(\ref{eqn:baseline}) at each step. 

\begin{equation}
r(x) =
\begin{cases}
\gamma \cdot g(x), & \text{if } g(x) > c \\
g(x), & \text{otherwise}
\end{cases}
\label{eqn:reward_func}
\end{equation}

\begin{equation}
R_t = r(\pi(a_t \mid s_t, \theta))
\label{eqn:return}
\end{equation}

\begin{equation}
R_{b,t} = r\left( \argmax_{a_t} \pi(a_t \mid s_t, \theta) \right)
\label{eqn:baseline}
\end{equation}

\subsection{Loss Function}
We modified the original loss function of the REINFORCE algorithm by adding an entropy regularization term to promote action diversity to penalize highly deterministic policies, shown in (\ref{eqn:loss}):
\begin{equation}
    \mathcal{L} = - \frac{1}{T} \sum_{t=1}^{T} (R_t - R_{b,t}) \log \pi(a_t | s_t, \theta) + \beta \mathcal{H}(\pi(\cdot | s_t, \theta)),
    \label{eqn:loss}
\end{equation}
% \vspace{-5pt}
where $\theta$ is the agent's parameters, $R_t$ is the sampled rewards at step t (see (\ref{eqn:return})), $R_{b,t}$ is the baseline reward at step t (see (\ref{eqn:baseline})), $\pi(a_t | s_t, \theta)$ is the sampled policy, $\beta$ is the entropy regularization factor, $\mathcal{H}(\pi(\cdot | s_t, \theta))$ is the policy entropy.


\section{Experiment Settings and Results}
\label{sec:3}
In this section, we describe our training methodology in detail, along with our evaluation approach and results.

\subsection{Training Details}
We used 50 prompts from the lmsys-chat-1m dataset \cite{zheng2023lmsys} with lengths between 50 and 120 characters. To ensure correctness, we ensured that adding the harmful intent did not incite unpleasant behavior before adding the adversarial suffix, and did not contain any derogatory remarks or attempts to induce objectionable behavior (e.g., bomb-making), before splitting them into train and test sets of 30 and 20 prompts, respectively.

In our experiments, we used the LLaMA-2 \cite{touvron2023llama} LLM as our victim model, training our RL agent to generate adversarial suffixes. We adjusted the target LLM parameters (llama-2-7b-chat-hf) to produce deterministic responses. At each step, we randomly sample a batch of 48 user inputs (with replacement) to generate adversarial suffixes (see Fig.~\ref{fig:rl_agent_env} step 2). We then combined the suffixes with a harmful intent prompt (scolding the user), appended it to the user's input before passing it through the LLM and scoring its output (See Fig.~\ref{fig:rl_agent_env} steps 3-6). In our experiments, we trained our agent using one NVIDIA A100 40GB GPU housed on-premise for 12 hours. In order to load the LLM model, we used 4-bit quantization, with quant-type as ``nf4", and compute data-type as ``bfloat16".
Our training concluded when the sampled rewards converged, stabilizing at a high number (see Fig.~\ref{fig:llama-2-exp-metric-plots}). This indicates that the agent has learned to favor adversarial tokens that are able to carry out the attacker's harmful intent. 

\subsection{Adversarial Suffix Evaluation}
In this section, we present the performance of adversarial suffixes generated by our agent on the test set when applied to LLaMA-2 and other large language models (LLMs) through transferability attacks. The transferability attacks mirror real-world scenarios in which malicious actors attempt to exploit vulnerabilities in open-source LLMs.
% , hoping that similar exploits will transfer to closed-source models. 
In our experiments, LLaMA-2 serves as the primary target for attack.

\subsubsection{Adversarial Suffix against LLaMA-2}
We define an attack as successful if the LLM shows the intent of scolding the user. When we launched our test set prompts against LLaMA-2, we achieved an ASR of 75\%. This indicates that our agent has learned to generate adversarial suffixes to bypass the guardrails. 

\begin{figure}[tbp]
\centering
\includegraphics[width=0.8\textwidth]{Chapter 2/5/llama_2_exp_metric_plots.png}
\caption{Experimental Metrics for LLaMA-2}
\label{fig:llama-2-exp-metric-plots}
\end{figure}

\subsubsection{Transferring Adversarial Suffixes to Other LLMs}
Having trained our adversarial agents, we investigated whether the adversarial suffixes, generated by our agent trained against the LLaMA-2 LLM, could be transferred to other LLMs. More specifically, we evaluated against a closed-source, publicly facing LLM and also an open-source LLM. Additionally, we used prompts in both the train and test sets to study the impact of attack effectiveness between seen and novel prompts, with respect to the agent. We used Gemini Pro as our target for the publicly facing LLM. 

\begin{figure}[tbp]
\centering
\begin{subfigure}[b]{0.95\textwidth}
    \centering
    \includegraphics[width=\textwidth]{Chapter 2/5/Bard_scolding_1_without_adv_suffix.png}
    \caption{Example Prompt without Adversarial Suffix.}
    \label{subfig:bard_scolding_no_suffix}
\end{subfigure}

\vspace{1em} % Space between subfigures

\begin{subfigure}[b]{0.95\textwidth}
    \centering
    \includegraphics[width=\textwidth]{Chapter 2/5/Bard_scolding_1.png}
    \caption{Example Prompt with Adversarial Suffix.}
    \label{subfig:bard_scolding_with_suffix}
\end{subfigure}

\caption{Impact of using Adversarial Suffix against Gemini Pro.}
\label{fig:withwithoutscold}
\end{figure}

\subsubsection{Transferability Attack Settings}
As Gemini Pro LLM has its own set of guardrails\footnote{Evaluated on 5th Jan 2024. We note that the LLM's response to our adversarial suffixes can vary over time, being dependent on Google.}, we removed our custom guardrails used during training when testing on Gemini Pro. For the open-source LLM, we used the Vicuna-33b model \cite{zheng2024judging}, and the Chatbot Arena \cite{chiang2024chatbot} platform to evaluate the LLM outputs. We set the LLM output parameters as such: temperature was set to 1, $top_p$ was set to 1 and the maximum output tokens was set at 512.  

As a baseline, we adopted a recently proposed white-box attack, namely the Greedy Coordinate Gradient (GCG) \cite{zou2023universal}, by transferring the generated adversarial suffixes, derived from LLaMA-2, against the two LLMs. As more models are derived from the fine-tuning of pre-trained models, this evaluation approach is highly valid. Attackers can get access to open-sourced LLMs for conducting white-box attacks, before transferring them to the intended victims.

For the GCG attack, we used a batch size of 400, $top_k$ of 200, and each prompt was optimized for 500 steps. If GCG was unable to find a successful adversarial suffix against LLaMA-2, we deem it as a failure case when computing ASR. In our experiments, we found 5 prompts from the train set and 2 prompts from the test set were not successful, with our GCG attack parameters.


\begin{table}[tbp]
\centering
\begin{tabular}{lccc}  % No vertical bars
\toprule
\textbf{Data Split} & \textbf{Model} & \textbf{ASR (GCG)} & \textbf{ASR (ours)} \\ 
\midrule
\multirow{2}{*}{\begin{tabular}[c]{@{}c@{}}Train\\(30 prompts)\end{tabular}} 
    & Gemini Pro & 16.7\% & 70.0\% \\ 
\cmidrule(lr){2-4}
    & Vicuna-33b & 30.0\% & 53.3\% \\ 
\midrule
\multirow{2}{*}{\begin{tabular}[c]{@{}c@{}}Test\\(20 prompts)\end{tabular}} 
    & Gemini Pro & 15.0\% & 26.7\% \\ 
\cmidrule(lr){2-4}
    & Vicuna-33b & 25.0\% & 40.0\% \\ 
\bottomrule
\end{tabular}
\caption{ASR of transferred adversarial prompts, using the adversarial agent trained on the LLaMA-2 victim and evaluated on respective targeted LLMs.}
\label{table:transferasr}
\end{table}


\begin{figure}[tbp]
\centering
\begin{subfigure}[b]{0.95\textwidth}
    \centering
    \includegraphics[width=\textwidth]{Chapter 2/5/Bard_scolding.png}
    \caption{Example Prompt 1.}
    \label{subfig:bard_scolding}
\end{subfigure}

\vspace{1em} % Space between subfigures

\begin{subfigure}[b]{0.95\textwidth}
    \centering
    \includegraphics[width=\textwidth]{Chapter 2/5/Bard_scolding_2.png}
    \caption{Example Prompt 2.}
    \label{subfig:bard_scolding_2}
\end{subfigure}

\caption{Further examples of prompts using Adversarial Suffix against Gemini Pro.}
\label{fig:bard_scolding_eg}
\end{figure}


\subsubsection{Transferability Attack Results}

As evident from Table~\ref{table:transferasr}, our generated suffixes are able to incite unpleasant responses, with an ASR of 70\% on the train set prompts and 26\% on the test set prompts against Gemini Pro. Against the Vicuna-33b, an ASR of 53\% on the train set prompts and 40\% on the test set prompts were obtained. This shows that the generated adversarial suffix can also be effective on models apart from the one it was trained on, albeit being more pronounced in the train rather than the test splits. 

We postulate that this is due to the adversarial suffixes being optimized solely on the train prompts. 
Regardless, our method still performed better than the prompts generated by our baseline, in both the train and test splits. We show more examples of successful attacks against Gemini Pro in Fig.~\ref{fig:bard_scolding_eg}. Additionally, we performed a sanity check of our adversarial suffixes. Fig.~\ref{subfig:bard_scolding_with_suffix} shows Gemini Pro being susceptible to our adversarial suffixes. Conversely, without the adversarial suffixes, Gemini Pro refused to scold the user (see Fig.~\ref{subfig:bard_scolding_no_suffix}). 


\section{Conclusion}
\label{sec:4}
In this work, we propose a novel black-box approach to perform prompt injection attacks with policy-based RL under our assumed threat model, to induce unpleasant responses from some targeted LLM. Our results show that our approach is successful in breaking LLaMA-2, using data from an open-source dataset. Moreover, our generated adversarial suffixes are transferable to other state-of-the-art LLMs, beating the GCG baseline in our transferability experiments. We reiterate that our main novelty lies in the approach of conducting black-box attacks against LLMs to induce unpleasant responses, and not the algorithm itself. Future work entails incorporating a more state-of-the-art RL algorithm to train our agent and training adversarial agents against other LLMs.


\input{references}
% \end{document}
