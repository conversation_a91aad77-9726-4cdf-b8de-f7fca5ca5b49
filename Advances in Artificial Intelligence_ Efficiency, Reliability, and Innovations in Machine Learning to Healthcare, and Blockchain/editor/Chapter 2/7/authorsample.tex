

\title*{Enhanced Robustness by Symmetry Enforcement
% \thanks{Applicable funding agency here. }
%Improving Robustness of Convolutional Neural Networks through Symmetry Enforcement
}
\author{
<PERSON><PERSON> \and
<PERSON><PERSON><PERSON> \and
<PERSON>\and
<PERSON> \and
<PERSON><PERSON><PERSON><PERSON>
}

\institute{
<PERSON><PERSON> \at Department of Computer Science, University of South Dakota, USA \\
\email{<EMAIL>}
\and
<PERSON><PERSON><PERSON> \at Department of Computer Science, University of South Dakota, USA \\
\email{<EMAIL>}
\and
<PERSON> \at Department of Computer Science, University of South Dakota, USA \\
\email{<EMAIL>}
\and
<PERSON> \at Department of Computer Science and Software Engineering, Auburn University, USA \\
\email{<EMAIL>}
\and
<PERSON><PERSON><PERSON><PERSON> \at Department of Computer Science and Software Engineering, Auburn University, USA \\
\email{<EMAIL>}
}
%
% Use the package "url.sty" to avoid
% problems with special characters
% used in your e-mail or web address
%
\maketitle

\abstract*{Convolutional Neural Networks excel in various applications but remain susceptible to adversarial attacks. Even tiny alterations, such as a single-pixel shift, could drastically mislead cutting-edge state-of-the-art models. In this study, we explore this vulnerability -- the adversarial example problem -- attributing it primarily to limited training samples. Such a case leads to overfitting and deviation from optimal models. To overcome this challenge, we propose integrating a variety of symmetry-invariant operations into CNN designs. This strategy maximizes the use of available training data, amplifies the neural network's expressive capacity, and empowers its robustness. Our experiments demonstrate the effectiveness of this approach against random perturbations in test data while concurrently enhancing their generalization capabilities. Overall, by augmenting CNN architectures with symmetry-invariant layers, we strive to mitigate vulnerabilities, enhancing both robustness and generalization adaptability.}

\abstract{Convolutional Neural Networks excel in various applications but remain susceptible to adversarial attacks. Even tiny alterations, such as a single-pixel shift, could drastically mislead cutting-edge state-of-the-art models. In this study, we explore this vulnerability -- the adversarial example problem -- attributing it primarily to limited training samples. Such a case leads to overfitting and deviation from optimal models. To overcome this challenge, we propose integrating a variety of symmetry-invariant operations into CNN designs. This strategy maximizes the use of available training data, amplifies the neural network's expressive capacity, and empowers its robustness. Our experiments demonstrate the effectiveness of this approach against random perturbations in test data while concurrently enhancing their generalization capabilities. Overall, by augmenting CNN architectures with symmetry-invariant layers, we strive to mitigate vulnerabilities, enhancing both robustness and generalization adaptability.}

\section{Introduction}
Convolutional Neural Networks (CNNs) have emerged as the go-to architecture for complex computer vision tasks, ranging from image classification to object detection, and have been used extensively as a backbone for various advanced artificial intelligence (AI) systems \cite{b2}\cite{b4}. However, these foundational models remain susceptible to adversarial examples—subtle input perturbations that mislead models\cite{b6}. As these attacks exploit the inherent sensitivity of CNNs to subtle changes in input, they pose a significant challenge to the deployment of these networks in safety-critical applications and have become a critical concern in designing reliable and trustworthy AI systems. While several defense mechanisms have been proposed, \textit{e}.\textit{g}.\ data augmentation \cite{b5}, the persistent nature of the adversarial example problem calls for innovative solutions that overcome the limitations of existing approaches, address the root causes of vulnerability, and are immune against optimization-crafted perturbations. To address this, we propose an architectural advancement in deep neural networks, shifting the focus from data augmentation to symmetry enforcement as a guiding principle in network design through the integration of diverse symmetries—translation, rotation, and scaling. By embedding a symmetry structure into CNN, our approach not only improves the utilization of training data but also reinforces the network's robustness against adversarial perturbations. The motivation for symmetry enforcement stems from the observation that symmetric structures exhibit inherent stability and resilience in the face of variations. By integrating CNN architectures with symmetrical characteristics, we aim to reduce the impact of adversarial perturbations on model predictions, thereby fortifying the reliability of these models in real-world settings.


\begin{figure}[ht]
\centering
\includegraphics[height = 1.2in, width=0.98\columnwidth]{Chapter 2/7/figure/symm.PNG}
\caption{The proposed symmetry enforcement method integrates rotation and scale invariant layers into the CNN.}
\label{exp1}
\end{figure}

\section{Proposed Symmetry Enforcement Method for Convolutional Neural Networks} 
\label{proposed_model}
We aim to enhance generalization in CNNs by integrating symmetries to approximate perturbation invariance. Symmetry in objects refers to unchanged properties despite transformations like rotation, scaling, or translation. In neural networks, consistent outputs under such transformations yield robustness.

\subsection{Rotation Invariance}
Rotation invariance is achieved using group-equivariant convolution (G-convolution) \cite{b1}, which applies rotated versions of filters to inputs. This ensures consistent output regardless of the input's orientation.

\subsection{Scale Invariance}
Scale invariance is achieved using scale-invariant convolution layers \cite{b3}. These layers divide the feature maps into multi-scale bins and apply pooling operations, allowing the model to process variable-size patterns uniformly.

\subsection{Translation Invariance}
CNNs inherently support translation invariance through convolution and pooling operations. This property allows CNNs to generalize across spatially shifted versions of input data.


\section{Experimental Setup, Results, and Discussion}
To assess the effectiveness of our proposed symmetry enforcement technique and its impact on robustness and generalization, we implement a symmetry-enforced CNN architecture and compare it with a baseline traditional CNN without symmetry enforcement. The symmetry-enforced model incorporates architectural symmetry as detailed in Section \ref{proposed_model}. We utilize CIFAR-100 as a benchmark dataset which comprises 60,000 32x32 color images in 100 classes for object recognition. The CNN models, both with and without symmetry enforcement, are trained on CIFAR-100 using standard training procedures. Symmetry constraints, where applicable, are integrated into the training process which was conducted on a single node HPC\footnote{Computation was performed on Lawrence Supercomputer at University of South Dakota awarded by 

\href{https://nsf.gov/awardsearch/showAwardAWD_ID=1626516&HistoricalAwards=false}{NSF.1626516}}. 

%\begin{figure}[htbp]
%\centerline{\includegraphics[width=9cm, height=2cm]%{figure/symm.PNG}}
%\caption{Integration of rotation and scale invariant layer in %SymmetryinvariantCNN model.}
%\label{exp1}
%\end{figure*}


In Fig.\ref{exp2}, the symmetry-enforced CNN model learns better and achieves higher accuracy during training. It shows a better generalization on the testing set initially, but the fluctuating accuracy indicates potential overfitting or instability in learning as the epochs increase. The better performance during training suggests that the symmetry invariance property is beneficial for the model's ability to learn from the data. The baseline CNN model is outperformed by the symmetry-enforced model during training. Its test accuracy drops significantly early and does not recover, which may indicate a lack of robustness or an inability to generalize as effectively as the symmetry-enforced model. The symmetry-enforced model's accuracy on perturbed data, although slightly lower than on the original data, does not show a significant drop. This indicates that the model has a degree of invariance to the introduced noise or distortions. The robustness to perturbations could be attributed to the model's ability to capture the essential features of the data that are invariant to the perturbations, suggesting that it has learned a more generalized representation.
%\begin{figure}[htbp]
%\centerline{\includegraphics[width=10cm, height=5cm]{figure/original.png}}
%\caption{Comparison in test accuracy and training convergence based on CIFAR 100 data.}
%\label{exp2}
%\end{figure}
\begin{figure}[tbp]
\centering\includegraphics[width=0.99\columnwidth]{Chapter 2/7/figure/original.png} 
\caption{Comparison using CIFAR 100 data:test accuracy and training convergence}
\label{exp2}
\end{figure}

\begin{figure}[tbp]
\centering
\begin{tabular}{cc}
\includegraphics[height = 1.5in, width=0.35\columnwidth]{Chapter 2/7/figure/rotate1} & \includegraphics[height= 1.5in, width=0.45\columnwidth]{Chapter 2/7/figure/rotate2} 
\end{tabular}
\caption{Comparison using transformed CIFAR 100 data: test accuracy}
\label{exp3}
\end{figure}



The symmetry-enforced model demonstrates superior performance on the random rotated CIFAR 100 data in Fig.\ref{exp3}. Its higher accuracy suggests that it is better at handling the transformations applied to the dataset, likely due to its architecture being more robust to variations in the input data.
Despite limited training data, our symmetry-enhanced model demonstrates improved expressive capability, thus increasing its resilience to adversarial alterations.

\section{Conclusion}
In this work,  we proposed a symmetry enforcement method to enhance the generalization and robustness of CNNs against adversarial examples. By incorporating various symmetries, such as rotation and scaling, into existing CNN models to improve their robustness, this approach leads to the development of perturbation invariance within the models. As a result, the enhanced models demonstrate greater generalizability to inputs that are shifted, rotated, or scaled. The introduction of symmetry operations not only optimizes the use of training data but also significantly expands the expressive capabilities of the network, contributing to increased adversarial robustness, and showcasing the potential of symmetry to create more versatile, reliable, and trustworthy AI systems.
\input{references}
% \end{document}
