%%%%%%%%%%%%%%%%%%%% author.tex %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
% sample root file for your "contribution" to a contributed volume
%
% Use this file as a template for your own input.
%
%%%%%%%%%%%%%%%% Springer %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


%% RECOMMENDED %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%\documentclass[graybox]{svmult}
%
%% choose options for [] as required from the list
%% in the Reference Guide
%
%\usepackage{mathptmx}       % selects Times Roman as basic font
%\usepackage{helvet}         % selects Helvetica as sans-serif font
%\usepackage{courier}        % selects Courier as typewriter font
%\usepackage{type1cm}        % activate if the above 3 fonts are
                             % not available on your system
%
%\usepackage{makeidx}         % allows index generation
%\usepackage{graphicx}        % standard LaTeX graphics tool
%                             % when including figure files
%\usepackage{multicol}        % used for the two-column index
%\usepackage[bottom]{footmisc}% places footnotes at page bottom
%
%% see the list of further useful packages
%% in the Reference Guide
%
%\makeindex             % used for the subject index
%                       % please use the style svind.ist with
%                       % your makeindex program
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
%\begin{document}

\title{Reproducibility Analysis for Results of Coupled Tensor Decompositions Based on Federated Learning}
% Use \titlerunning{Short Title} for an abbreviated version of
% your contribution title if the original one is too long
% \author{Yukai Cai, Hang Liu, Xiulin Wang, Fengyu Cong}
\author{Yukai Cai\inst{1,2} \and
Hang Liu\inst{1,2,*} \and
Xiulin Wang\inst{3,4,*} \and Fengyu Cong\inst{1,2,5,6}}
%
\authorrunning{Y. K. Cai et al.}
% Use \authorrunning{Short Title} for an abbreviated version of
% your contribution title if the original one is too long
% \institute{Yukai Cai (cai\<EMAIL>), Hang Liu (<EMAIL>), Fengyu Cong (<EMAIL>) \at School of Biomedical Engineering, Faculty of Medicine, Dalian University of Technology, Dalian, China,~Key Laboratory of Integrated Circuit and Biomedical Electronic System, Liaoning Province, Dalian University of Technology, Dalian, China 
% \at 
% Fengyu Cong is also at
% Faculty of Information Technology, University of Jyv$\ddot{a}$skyl$\ddot{a}$,Jyv$\ddot{a}$skyl$\ddot{a}$, Finland \and Key Laboratory of Social Computing and Cognitive Intelligence (Dalian University of Technology), Ministry of Education, China 
% \and Xiulin Wang (<EMAIL>) \at
% Stem Cell Clinical Research Center, the First Affiliated Hospital of Dalian Medical University, Dalian, China \and Dalian Innovation Institute of Stem Cell and Precision Medicine, Dalian, China
% }
\institute{
$^1$School of Biomedical Engineering, Faculty of Medicine, Dalian University of Technology, Dalian, China \at $^2$Key Laboratory of Integrated Circuit and Biomedical Electronic System, Liaoning Province, Dalian University of Technology, Dalian, China 
\at $^3$Stem Cell Clinical Research Center, the First Affiliated Hospital of Dalian Medical University, Dalian, China 
\at $^4$Dalian Innovation Institute of Stem Cell and Precision Medicine, Dalian, China 
\at $^5$Faculty of Information Technology, University of Jyv$\ddot{a}$skyl$\ddot{a}$,Jyv$\ddot{a}$skyl$\ddot{a}$, Finland 
\at $^6$Key Laboratory of Social Computing and Cognitive Intelligence (Dalian University of Technology), Ministry of Education, China
\at
emails: cai\<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>
}
%
% Use the package "url.sty" to avoid
% problems with special characters
% used in your e-mail or web address
%
\maketitle


\abstract{Since most of the current tensor decompositions use iterative algorithms, these algorithms can theoretically converge to the global optimal solution, but in practice, due to noise and other factors these algorithms tend to converge to different local optimal solutions, so there is a need to analyse the reproducibility of tensor decompositions in order to ensure the reliability of the algorithmic results. Based on this, in this paper, according to the previously developed coupled tensor decomposition algorithm based on federated learning (FCNCP), its decomposition results in simulated and real data are subjected to reproducibility analysis, so as to verify the stability of the FCNCP algorithm. In this study, we have used both simulated and real data for our experiments. The reproducibility metrics of the 50 decomposition results of the simulated data reached 1.0. Whereas the results of 50 decompositions using the fifth-order ERP tensor data collected by applying proprioceptive stimuli to the right and left hands, the reproducibility metrics for each of the 10 components of interest selected were 0.92, 0.8, 0.82, 0.8, 0.82, 0.57, 0.94, 0.91, 0.79, 0.94 respectively. This shows that our previously studied FCNCP algorithm is stable and effective.}


%
%
%
\section{Introduction}
Hitchcock initially introduced the concept of tensors in literature \cite{1927The}. Put simply, tensors act as multidimensional arrays, expanding beyond vectors and matrices into higher-dimensional spaces, offering a more intuitive portrayal of the intricate structural features present in high-dimensional data. Signal processing and data analysis, from their vantage point, involve the consideration of information across various dimensions like time, space, and frequency. The resultant factor matrices or latent components carry specific physical and physiological meanings. Consequently, tensor decomposition finds broad utility across diverse domains such as signal processing and machine learning, with a growing presence in brain signal processing and cognitive neuroscience \cite{2017Tensor,2015Tensor,2016Linked,2011application, 2014lowrank}. Canonical Polyadic (CP) \cite{1927The} and Tucker \cite{1966some} models stand out as the two frequently employed tensor decomposition models. CP dissects tensors into a sequence of rank-one tensors, while Tucker expresses tensors as a blend of a core tensor and a sequence of factor matrices. Both models adhere strictly to multilinear assumptions, which may not universally apply in practical data handling. The PARAFAC2 model \cite{1999para} introduces evolving factors to shatter the multilinear assumption, enabling tensor data to evolve within a mode over time, thus facilitating the tracking of evolving brain connectivity networks. The advent of coupled tensor decomposition in recent times extends tensor decomposition to jointly scrutinize multiple tensor data, tackling joint blind source separation issues \cite{2016Linked}. In the realm of distinct tensor data samples, coupled tensor decomposition harnesses its high-order and multi-coupling attributes for joint analysis, unearthing richer shared and distinct information among samples. This enriches the understanding of brain function, unveiling potential inner workings linking brain function to various cognitive tasks or brain disorders. Numerous studies have successfully conducted joint analysis of EEG and fMRI data employing coupled matrix-tensor factorization (CMTF \cite{2013structure}) and its variations \cite{2019unravel,2020group,2022exploring}. EEG data manifests as three-dimensional tensors, while fMRI data takes shape as two-dimensional matrices, assuming shared structures across time or subject dimensions \cite{2017tensor,2004concurrent,2017tensorbased,2020extraction}.

To address the issue of the inability of current tensor decomposition techniques to jointly analyze "island" data dispersed across multiple servers, researchers have proposed federated tensor decomposition models. Leveraging the characteristics of federated learning where servers do not need to exchange raw data, ensuring that local servers all participate in training to jointly establish models while safeguarding the privacy of the original data. Kim et al. were the first to utilize a federated tensor decomposition model to conduct phenotype analysis of massive electronic health records across hospitals without sharing patient-level data. Each hospital's data can be represented as a three-way tensor of patients, drugs, and diagnoses. Through secure data coordination based on the alternating direction method of multipliers and joint computation steps, the federated tensor decomposition was demonstrated to be comparable to centralized training models in accuracy and phenotype mining performance while respecting patient privacy \cite{2017federated}. In earlier work, a coupled tensor decomposition model based on the federated framework was proposed, and its effectiveness was demonstrated through both simulated and real data \cite{2024FCNCP}.

However, due to the low signal-to-noise ratio of real EEG data, parameter selection, and optimization issues with the algorithms themselves, algorithms may converge only to local optima during the decomposition process. Local optima lack uniqueness, necessitating reproducibility analysis of results from multiple runs to assess the reliability of algorithmic results. In this study, reproducibility is specifically defined as the consistency of multiple decomposition results obtained by applying the same algorithm to the same data, independent of initial point selection and algorithm parameter settings. Ideally, applying the same algorithm to the same data multiple times should yield identical results. If the algorithm's results are not reproducible or have low reproducibility, their credibility in practical data analysis would be low, making them difficult to use for actual data analysis \cite{2021common}. Therefore, the reproducibility of algorithmic results is crucial for data analysis, and in recent years, there has been increasing attention to the reproducibility of research results in cognitive neuroscience. However, traditional methods for assessing the reproducibility of blind source separation often focus on a single dimension. For example, the ICASSO toolbox clusters source signals from multiple ICA decompositions of the same data, providing a quantitative description of ICA's reproducibility \cite{2004validating}. Other studies on the reproducibility of ICA decomposition results include cross-validation and test-retest reliability, but these are based on the dimensionality of the source signals, overlooking the coefficient matrices corresponding to the source signals \cite{2013Neuro,2010Reliable}. With the popularity of tensor decomposition and coupled tensor decomposition methods in various fields, attention has gradually shifted to studying the reproducibility of component acquisition. However, due to the complexity of the algorithmic processing and incomplete basic definitions, reproducibility analysis of tensor algorithms is still in the developmental stage and has not yet produced mainstream methods applicable to neuroimaging data processing. Hence, there are not many research results on this issue. For reproducibility analysis of tensor components, clustering algorithms based on tensor space need to be adopted. Most tensor clustering algorithms are currently based on iterative optimization. For example, Zeng et al. transformed the clustering problem of multidimensional tensors into the problem of estimating the least squares of multiple different tensor blocks \cite{2019Multiway}. Rastogi et al. reconstructed the clustering model into a binary tree structure with a square loss function \cite{2017Tree}. Forero et al. proposed a co-clustering method based on the Tucker decomposition model \cite{2019Co}. To adapt tensor spectral co-clustering to reproducibility analysis of component acquisition by tensor decomposition algorithms, researchers have proposed a novel tensor spectral clustering implementation process. By defining transfer tensors synthesizing reproducibility information from various dimensions, and leveraging the uniqueness advantage of high-order singular value decomposition (HOSVD) combined with hierarchical clustering, spectral clustering effectiveness is achieved \cite{2022Discorvering}.

In the study of this paper, we conduct a reproducibility analysis of the predeveloped federated framework-based coupled tensor decomposition algorithm (FCNCP)\cite{2024FCNCP}. Simulated data and real data are used in order to verify the effectiveness and stability of the algorithm. Thus, a solution is given for the joint analysis of multidimensional data from multiple centres.


\section{Methods}

\subsection{Coupled tensor decomposition}
Coupled tensor decomposition is a method for simultaneous decomposition of multiple tensors. While traditional tensor decomposition methods can only handle a single tensor, coupled tensor decomposition can efficiently handle multiple related tensors. In coupled tensor decomposition, we consider the correlations between multiple tensors and use these correlations to improve the accuracy and efficiency of the decomposition.

Assuming that there are $S$ tensor blocks ${{\chi }^{\left( s \right)}}$, the corresponding CP decomposition is denoted as ${{\chi }^{\left( s \right)}}\simeq \sum\nolimits_{r=1}^{{{R}^{\left( s \right)}}}{u_{r}^{\left( 1,s \right)}\circ u_{r}^{\left( 2,s \right)}\circ \cdots \circ u_{r}^{\left( N,s \right)}}$, and the objective function is constructed considering the non-negativity of the constructed tensor and the coupling constraints in the factor matrix:

\begin{align}
    \begin{split}
        & \min \sum\limits_{s=1}^{S}{\left\| {{\chi }^{\left( s \right)}}-\sum\nolimits_{r=1}^{{{R}^{\left( s \right)}}}{u_{r}^{\left( 1,s \right)}\circ u_{r}^{\left( 2,s \right)}\circ \cdots \circ u_{r}^{\left( N,s \right)}} \right\|_{F}^{2}} \\ 
        & s.t.\text{ }u_{r}^{\left( n,1 \right)}=u_{r}^{\left( n,2 \right)}=\cdots =u_{r}^{\left( n,S \right)},r\le {{L}_{n}} \\ 
    \end{split}
    \label{equation1}
\end{align}
where $N$ denotes the number of tensor dimensions, ${{R}^{\left( s \right)}}$ denotes the number of components of the $s$th tensor block, and $S$ denotes the number of tensor blocks.

 \begin{figure*}[htb]
	\centering
\includegraphics[width=12cm]{fig002/fig1}
	\caption{Schematic diagram of the coupled tensor decomposition framework based on federated learning.}
	\label{fig1}
\end{figure*}

\subsection{A Coupled Nonnegative CANDECOMP/PARAFAC
Decomposition Based on Federated Learning}
For the data in different clients, due to the problems of privacy protection and local policies and regulations, the problem of "data silo" is caused. According to this, literature \cite{2024FCNCP} introduces a coupled tensor decomposition based on federated learning to solve the problem that data in different clients are not interoperable. The coupling constraints in coupled tensor decomposition are constructed using elastic mean federated learning, which preserves the advantages of coupled tensor decomposition in cross-sample tensor data analysis and protects data privacy. The process of the FCNCP algorithm is shown in Fig.~\ref{fig1}. The objective function of the model is as follows:

\begin{align}
    \begin{split}
    & \min L=\sum\limits_{k=1}^{K}{\left\| {{\chi }_{k}}-\sum\nolimits_{r=1}^{{{R}_{k}}}{u_{r,k}^{\left( 1 \right)}\circ u_{r,k}^{\left( 2 \right)}\circ \cdots \circ u_{r.k}^{\left( N \right)}} \right\|_{F}^{2}}\\
    &+\frac{\rho }{2}\sum\limits_{k=1}^{K}{\sum\limits_{n=1}^{N}{\sum\limits_{r=1}^{{{L}_{n}}}{\left\| u_{r,k}^{\left( n \right)}-\tilde{u}_{r}^{\left( n \right)} \right\|_{2}^{2}}}} \\ 
    & s.t.\text{ }u_{r,k}^{\left( n \right)}\ge 0\text{ }for\text{ }n=1,2,\cdots N
    \end{split}
    \label{equation3}
\end{align}

where $\tilde{u}_{r}^{\left( n \right)}$ is the global model and $K$ is the number of clients. This distributed optimisation method has two objectives, one is to make the loss function local to each client to be minimised, and the second is that the gap between the local model on each client and the global model on the central server is desired to be relatively small.

\subsection{Reproducibility analysis based on tensor spectral clustering}
Inspired by the literature \cite{2022Discorvering}, the reproducibility analysis of the components extracted from the coupled tensor decomposition will be achieved by performing a cluster analysis on the results of multiple runs. Specifically, a third-order EEG tensor of model order $R$ can be decomposed by tensor decomposition to generate $R$ rank-one tensors, each of which can be viewed as an outer product of time, space, and subject component vectors. Assuming that the algorithm is run $K$ times, $K\times R$ rank-one tensors are obtained. The method then forms a transfer tensor by outer product of the adjacency matrices obtained for each dimension, aggregating the reproducibility information for each dimension. Subsequently, the HOSVD algorithm was used to decompose the transfer tensor, which in turn was combined with hierarchical clustering to achieve a spectral clustering effect, enabling the assessment of the reproducibility of the components. The methodology used in this paper is divided into the following main steps:

(1) For a given EEG tensor data, assume that the number of components is $R$, run the coupled tensor decomposition $K$ times independently at different initialisations, so that $R\times K$ components are obtained under each dimension;

(2) Pearson's correlation is used to calculate the similarity of each dimension to construct the weighted neighbour matrix, denoted as ${W}^{\left({A}^{\left(1\right)}\right)}$, ${W}^{\left({B}^{\left(1\right)}\right)}$, ${W}^{\left({C}^{\left(1\right)}\right)},{W}^{\left({A}^{\left(2\right)}\right)}$,${W}^{\left({B}^{\left(2\right)}\right)}$ and ${W}^{\left({C}^{\left(2\right)}\right)}\in\mathbb{R}^{RK\times RK}$;

(3) With reference to matrix spectral clustering, calculate the transfer matrix corresponding to each dimension i.e. ${\ {P}}^{\left(m\right)}={W}^{\left(m\right)T}{D}^{\left(m\right)^{-1}}$, where ${D} ^{\left(m\right)}=diag\left({W}^{\left(m\right)}{e}\right)$, $e$ is a vector with all elements 1;

(4) Construct the transfer tensor, which is defined as ${{P}={I}\\\times_1{P}}^{\left(1\right)}{\\ \times_2{P}}^{\left(2\right)}{\ \times_3{P}}^{\left(3\right)}$

(5) Using the HOSVD algorithm to reduce the dimensionality of the transfer tensor $P$, the decomposition of each dimension is essentially an eigenvalue decomposition of the unfolded tensor covariance matrix, so the tensor spectral clustering in each dimension can be simplified to the matrix spectral clustering, calculate and find the eigenvectors corresponding to the $R$ largest eigenvalues in the last dimension into a matrix i.e. ${V}=\left[ v_{1},v_{2}, \dots,v_{R} \right]$, and normalise row by row.

(6) Hierarchical clustering was used to cluster the rows of normalised $V$ into $R$ classes and assign the original samples.

(7) Average intra-class similarity in the clustering results as a reproducibility metrics for the components and output it.

The flow of the algorithm is shown in the Fig.~\ref{fig2}.

\begin{figure}[htb]
	\centering
	\includegraphics[width=6.2cm]{fig002/fig2}
	\caption{Flowchart of reproducibility analysis algorithm.}
	\label{fig2}
\end{figure}

\section{Experiments and results}
We performed 50 tensor decomposition experiments using both simulated third-order tensor data and fifth-order tensors formed from ERP data collected by applying proprioceptive stimuli to the left and right hands, and demonstrated the stability of the algorithm through reproducibility analyses of these decomposition results.

\subsection{Iteration stopping conditions}
Define the relative error/residual of FCNCP at the kth client, i-th iteration as:

\begin{equation}
    \text{RelErr}_{k}^{i}=\frac{{{\left\| {{\chi }_{k}}-\sum\nolimits_{r=1}^{{{R}_{k}}}{u_{r,k}^{\left( 1 \right),i}\circ u_{r,k}^{\left( 2 \right),i}\circ \cdots \circ u_{r.k}^{\left( N \right),i}} \right\|}_{F}}}{{{\left\| {{\chi }_{k}} \right\|}_{F}}}
\end{equation}

We terminate the FCNCP decomposition process when the following condition is satisfied between two iterations $i$ and $i + 1$.


\subsection{Simulate experiment}
We first applied the algorithm with simulated data. Two tensors of size 61 × 72 × 64, representing frequency × time × channel, were created, and the two tensor blocks were used as two clients as follows:

\begin{align}
    \begin{split}
    &{{\chi }_{\text{1}}}={{t}_{1}}\circ {{f}_{1}}\circ {{c}_{1}}+{{t}_{2}}\circ {{f}_{2}}\circ {{c}_{2}}+{{t}_{3}}\circ {{f}_{3}}\circ {{c}_{3}}\\
    &{{\chi }_{2}}={{t}_{1}}\circ {{f}_{1}}\circ {{c}_{4}}+{{t}_{2}}\circ {{f}_{2}}\circ {{c}_{5}}+{{t}_{4}}\circ {{f}_{4}}\circ {{c}_{6}}
    \end{split}
\end{align}

In the frequency and time patterns, four frequency components were constructed using a Hanning window centred at 15, 20, 40, 50 and white noise, and four time components were constructed using a Hanning window centred at 10, 20, 30, 40 and white noise, and coupling was established on top of that. Six channel components were formed by randomly selecting four adjacent brain electrodes and assigning them a random number between 0.5 and 1. The simulated data for the two tensor blocks (client side) are shown in Fig.~\ref{fig3a} and~\ref{fig3b}.

\begin{figure*}[htb]
\centering
% \vspace{-0.35cm}
% \setlength{\abovecaptionskip}{-2pt}
% \subfigtopskip=2pt
% \subfigbottomskip=7pt
% \subfigcapskip=-5pt

\subfigure[]{
\label{fig3a}
\includegraphics[width=5cm]{fig002/fig3a.pdf}}
\quad
\subfigure[]{
\label{fig3b}
\includegraphics[width=5cm]{fig002/fig3b.pdf}}
\subfigure[]{
\label{fig3c}
\includegraphics[width=5cm]{fig002/fig3c.pdf}}
\quad
\subfigure[]{
\label{fig3d}
\includegraphics[width=5cm]{fig002/fig3d.pdf}}
\caption{Illustration of the simulation experiment. (a) Tensor data for client 1. (b) Tensor data for client 2. (c) Results after FCNCP decomposition for client1. (d) Result after FCNCP decomposition for client2.}
\label{fig3}
\end{figure*}

\begin{figure}[htb]
\centering
\vspace{-0.35cm}
\setlength{\abovecaptionskip}{-2pt}
\subfigtopskip=2pt
\subfigbottomskip=7pt
\subfigcapskip=-5pt
\subfigure[]{
\label{fig4a}
\includegraphics[width=5cm]{fig002/fig4a.pdf}}
\quad
\hspace{-20pt}
\subfigure[]{
\label{fig4b}
\includegraphics[width=5cm]{fig002/fig4b.pdf}}
\caption{Illstraition of the reproducibility analysis of simulation experiments. (a) Reproducibility analysis of components in client 1. (b) Reproducibility analysis of components in client 2.}
\label{fig4}
\end{figure}


We ran 50 times of the FCNCP algorithm, and we obtained stable decomposition results with an averaged tensor fit of 0.996 for both client. At the same time, We performed a reproducibility analysis for each client component, and looking at the reproducibility metrics of the results of these 50 FCNCP decompositions, we found that the reproducibility metrics of the components in both clients is 1.0. This leads to the stability of the algorithms in this paper. The reproducibility metrics of each component and the number of components in the cluster are shown in Fig. ~\ref{fig4}.


\subsection{Real data experiment}
\subsubsection{Data description}
The ERP data in our experiments were obtained from the open preprocessing dataset associated with the ERPWAVELAB toolkit\cite{2007atool}, which can be downloaded from \href{www.erpwavelab.org}{www.erpwavelab.org}. These data were from proprioceptive experiments in which two conditions (left- and right-handed) were manipulated by increasing the handheld load. An important part of the stimulus was the change in force during static muscle contraction, which is considered a proprioceptive stimulus\cite{2006decomposing}. 14 subjects participated in the experiment and 64 scalp electrodes were used to record EEG data. Each subject performed a total of 360 trials (epochs) in each condition. All ephemeral data were transformed into time-frequency representation (TFR) by means of the complex Molette wavelet. In the wavelet transform, only the frequency band from 15 Hz to 75 Hz was analysed, with linear intervals of 1 Hz. Inter-trial phase coherence (ITPC) was then computed as an average spectral estimate over all trials\cite{2004EEGLAB}. Since the TFR is first applied to each trial and then averaged across trials is calculated, the ITPC can be regarded as an induced oscillation of the brain\cite{2006mechanisms}. Also, the ITPC only takes values between 0 and 1\cite{2004EEGLAB,2014analyzing}. Finally, a fifth-order non-negative tensor block (condition × subject × channel × frequency × time = 2 × 14 × 64 × 61 × 72) was generated, with 61 frequency points representing 15 to 75 Hz, and 72 time points representing 0 to 346.68 ms. Finally, a simulation experiment of federated learning was carried out in which the 14 subjects were divided into two clients, and assigned to seven subjects, respectively.

\subsubsection{Experiment setup}
Inspired by literature \cite{2024FCNCP}, We perform principal component analysis on the matrix data unfolded along the frequency mode for each block of data, keeping the number of components with 95\% cumulative variance explained, and set the number of components for the two clients to be \{43,45\}, respectively. At the same time, we choose to establish coupling constraints on time and frequency based on the correlation graphs in time, frequency and channel modes, and choose 15 as the number of coupling components.

\subsubsection{Result analyse}
We performed 50 tensor decompositions of the ERP fifth-order tensor data collected by applying proprioceptive stimuli to the left and right hands using the FCNCP algorithm, and performed reproducibility analyses of the components decomposed by each of the two clients, as shown in Fig~\ref{fig7}. The reproducibility metrics for each component were obtained. By selecting the centre components of each clustering cluster from both clients and ten centre components of interest were selected from both clients, which is shown in Figs.~\ref{fig5} and~\ref{fig6}. Where Fig.~\ref{fig5} shows the components of the beta-band oscillations and Fig.~\ref{fig6} shows the components of the gamma-band oscillations. Comparison with the literature \cite{2018extracting} reveals that coupled tensor decomposition analysis of EEG signals using the federated learning idea leads to the same conclusions as compared to decomposition analysis of all data. Their reproducibility metricses are \{0.92, 0.8, 0.82, 0.8, 0.82, 0.57, 0.94, 0.91, 0.79, 0.94\} respectively, these components of interest appeared \{47, 49, 50, 48, 48, 50, 50, 50, 50, 44\} times in the results of 50 runs of the algorithm. From the results, we see that the sixth component of interest we selected has a low reproducibility metric, while the rest of the metrics are above 0.79 and occur at least 44 times out of 50 runs. This result tells us that the algorithm's results in real datasets are still more stable.

\begin{figure*}
    \centering
    % \subfigtopskip=2pt
    % \subfigbottomskip=2pt
    % \subfigcapskip=-5pt
    \subfigure[]{\includegraphics[width=10cm]{fig002/fig5a.pdf}
    \label{fig5a}}
    \subfigure[]{\includegraphics[width=10cm]{fig002/fig5b.pdf}
    \label{fig5b}}
    \subfigure[]{\includegraphics[width=10cm]{fig002/fig5c.pdf}
    \label{fig5c}}
    \caption{Components of beta-band oscillations. (a) Activity appeared in the frontal lobe within 75 ms at 15 - 20 Hz. (b) Activity occurs in the temporal lobe at 15 - 20 Hz, in the region of 75 ms. (c) Activity occurs in the frontal lobe at 25 - 30 Hz, in the region of 60 ms.}
    \label{fig5}
\end{figure*}

\begin{figure*}[htb]
    \centering
    \subfigtopskip=2pt
    \subfigbottomskip=2pt
    \subfigcapskip=-5pt
    \subfigure[]{\includegraphics[width=12cm]{fig002/fig6a.pdf}
    \label{fig6a}}
    \subfigure[]{\includegraphics[width=12cm]{fig002/fig6b.pdf}
    \label{fig6b}}
    \caption{Components of gamma-band oscillations. (a) Activity occurs within the frontal lobe at 30 - 40 Hz, In the region of  60 ms. (b) Activity in the first component occurs between the right parietal and temporal lobes at 40 - 75 Hz and 50 ms. Activity in the second component occurs between the left parietal and temporal lobes at 40 - 75 Hz, in the region of 45 ms.}
    \label{fig6}
\end{figure*}

\begin{figure*}[htb]
\centering
\vspace{-0.35cm}
\setlength{\abovecaptionskip}{-2pt}
\subfigtopskip=2pt
\subfigbottomskip=7pt
\subfigcapskip=-5pt
	
\subfigure[]{
\label{fig7a}
\includegraphics[width=5.8cm]{fig002/fig7a.pdf}}
\quad
\hspace{-15pt}
\subfigure[]{
\label{fig7b}
\includegraphics[width=5.8cm]{fig002/fig7b.pdf}}
\caption{Illstraition of the reproducibility analysis of real data experiments. (a) Reproducibility analysis of components in client 1. (b) Reproducibility analysis of components in client 2.}
\label{fig7}
\end{figure*}

\section{Discussion}
In this study, we explore an important challenge of current tensor decomposition algorithms, namely their sensitivity to factors such as noise in practical applications, resulting in the tendency to converge to different locally optimal solutions. We recognise the need for reproducibility analysis for such algorithms, especially those using iterative methods, to ensure the reliability of the algorithmic results. With this in mind, this paper makes use of a previously developed coupled tensor decomposition algorithm in a federated framework (FCNCP) and provides an in-depth study and analysis of its decomposition results in both simulated and real data.

We begin by repeating the experiments on both simulated and real data, and find that the simulated data demonstrates perfect reproducibility (with a metric of 1.0) over 50 decompositions, a result that underscores the accuracy and stability of the FCNCP algorithm under idealised conditions. In contrast, the reproducibility metric in the real dataset, although slightly decreased, the average reproducibility metric of the relevant components was still as high as 0.831, and at least 44 out of 50 runs. These results reflect that the FCNCP algorithm maintains a high level of stability and reliability even when it is affected by noise and data complexity in real applications.

However, we are also aware that there are still some limitations of this study. We have only validated on a limited number of simulated and real datasets, which may not be fully representative of the algorithm performance in all cases. Secondly, despite the relatively high reproducibility metrics of the modelled data, there is still a gap compared to real data. This may indicate that the FCNCP algorithm may need further optimisation and tuning when dealing with real data that are complex and contain more noise. In addition, the sensitivity of the algorithms to noise may affect their generalisability and reliability in different application contexts.

In future work, we will develop an iterative subtraction coupled tensor decomposition algorithm based on the federated framework, which is proposed to use an iterative phase reduction strategy in the coupled tensor decomposition process. Using tensor spectral clustering based reproducibility analysis, the decomposition algorithm is run multiple times to identify components with high reproducibility and high energy, these components are removed from the tensor to be decomposed by tensor backprojection, and then the decomposition of the remaining tensor data is continued to extract new components and so on until the iterative stopping condition is satisfied. It is hoped that this method can solve the problem of unreliable results due to low reproducibility metrics when using tensor decomposition to process ERP data.

\section{Conclusion}
In summary, this paper verifies the high stability and reliability of the FCNCP algorithm by providing an in-depth analysis of the application of the algorithm to both simulated and real data. The perfect reproducibility of simulated data and the high reproducibility metrics of real data show that the FCNCP algorithm is still able to provide trustworthy results even in environments with complex and noisy data. This advantage gives it great potential for research and industrial applications that require high accuracy and stability. Future research will develop an iterative subtractive coupled tensor decomposition algorithm based on the federated framework, which will hopefully address the problem of untrustworthy results due to low reproducibility metrics when using tensor decomposition to process ERP data.

\input{references002}
%\end{document}
