This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=pdflatex 2025.3.22)  30 MAY 2025 14:27
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**"/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/Advances in Artificial Intelligence_ Efficiency, Reliability, and Innovations in Machine Learning to Healthcare, and Blockchain/editor/editor.tex"
(/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/Advances in Artificial Intelligence_ Efficiency, Reliability, and Innovations in Machine Learning to Healthcare, and Blockchain/editor/editor.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(./svmult.cls
Document Class: svmult 2024/03/15 v5.11 
Springer Verlag global LaTeX document class for multi authored books
Class Springer-SVMult Info: extra/valid Springer sub-package 
(Springer-SVMult)           not found in option list - using "global" style.
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@section=\count197
\c@subsection=\count198
\c@subsubsection=\count199
\c@paragraph=\count266
\c@subparagraph=\count267
\c@figure=\count268
\c@table=\count269
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
\svparindent=\dimen142
\bibindent=\dimen143
\betweenumberspace=\dimen144
\headlineindent=\dimen145
\minitoc=\write3
\c@minitocdepth=\count270
\c@chapter=\count271
\mottowidth=\dimen146
\svitemindent=\dimen147
\verbatimindent=\dimen148
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 975.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 976.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 977.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 978.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 979.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 980.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 981.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 982.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 983.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 984.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 985.
\tocchpnum=\dimen149
\tocsecnum=\dimen150
\tocsectotal=\dimen151
\tocsubsecnum=\dimen152
\tocsubsectotal=\dimen153
\tocsubsubsecnum=\dimen154
\tocsubsubsectotal=\dimen155
\tocparanum=\dimen156
\tocparatotal=\dimen157
\tocsubparanum=\dimen158
\foot@parindent=\dimen159
\c@theorem=\count272
\c@case=\count273
\c@conjecture=\count274
\c@corollary=\count275
\c@definition=\count276
\c@example=\count277
\c@exercise=\count278
\c@lemma=\count279
\c@note=\count280
\c@problem=\count281
\c@property=\count282
\c@proposition=\count283
\c@question=\count284
\c@solution=\count285
\c@remark=\count286
\c@prob=\count287
\instindent=\dimen160
\figgap=\dimen161
\bildb@x=\box52
\figcapgap=\dimen162
\tabcapgap=\dimen163
\c@merk=\count288
\c@@inst=\count289
\c@@auth=\count290
\c@auco=\count291
\instindent=\dimen164
\authrun=\box53
\authorrunning=\toks17
\tocauthor=\toks18
\titrun=\box54
\titlerunning=\toks19
\toctitle=\toks20
\c@contribution=\count292
LaTeX Info: Redefining \abstract on input line 2385.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/xcolor/x11nam.def
File: x11nam.def 2024/09/29 v3.02 Predefined colors according to Unix/X11 (UK)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amscls/amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks21
\thm@bodyfont=\toks22
\thm@headfont=\toks23
\thm@notefont=\toks24
\thm@headpunct=\toks25
\thm@preskip=\skip51
\thm@postskip=\skip52
\thm@headsep=\skip53
\dth@everypar=\toks26
LaTeX Info: Redefining \qed on input line 273.
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/framed/framed.sty
Package: framed 2011/10/22 v 0.96: framed or shaded text with page breaks
\OuterFrameSep=\skip54
\fb@frw=\dimen165
\fb@frh=\dimen166
\FrameRule=\dimen167
\FrameSep=\dimen168
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/makeidx.sty
Package: makeidx 2021/10/04 v1.0m Standard LaTeX package
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks27
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
)
\Gin@req@height=\dimen169
\Gin@req@width=\dimen170
) (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/multicol.sty
Package: multicol 2024/09/14 v1.9i multicolumn formatting (FMi)
\c@tracingmulticols=\count293
\mult@box=\box55
\multicol@leftmargin=\dimen171
\c@unbalance=\count294
\c@collectmore=\count295
\doublecol@number=\count296
\multicoltolerance=\count297
\multicolpretolerance=\count298
\full@width=\dimen172
\page@free=\dimen173
\premulticols=\dimen174
\postmulticols=\dimen175
\multicolsep=\skip55
\multicolbaselineskip=\skip56
\partial@page=\box56
\last@line=\box57
\mc@boxedresult=\box58
\maxbalancingoverflow=\dimen176
\mult@rightbox=\box59
\mult@grightbox=\box60
\mult@firstbox=\box61
\mult@gfirstbox=\box62
\@tempa=\box63
\@tempa=\box64
\@tempa=\box65
\@tempa=\box66
\@tempa=\box67
\@tempa=\box68
\@tempa=\box69
\@tempa=\box70
\@tempa=\box71
\@tempa=\box72
\@tempa=\box73
\@tempa=\box74
\@tempa=\box75
\@tempa=\box76
\@tempa=\box77
\@tempa=\box78
\@tempa=\box79
\@tempa=\box80
\@tempa=\box81
\@tempa=\box82
\@tempa=\box83
\@tempa=\box84
\@tempa=\box85
\@tempa=\box86
\@tempa=\box87
\@tempa=\box88
\@tempa=\box89
\@tempa=\box90
\@tempa=\box91
\@tempa=\box92
\@tempa=\box93
\@tempa=\box94
\@tempa=\box95
\@tempa=\box96
\@tempa=\box97
\@tempa=\box98
\c@minrows=\count299
\c@columnbadness=\count300
\c@finalcolumnbadness=\count301
\last@try=\dimen177
\multicolovershoot=\dimen178
\multicolundershoot=\dimen179
\mult@nat@firstbox=\box99
\colbreak@box=\box100
\mc@col@check@num=\count302
) (/usr/local/texlive/2025/texmf-dist/tex/latex/footmisc/footmisc.sty
Package: footmisc 2024/12/24 v6.0g a miscellany of footnote facilities
\FN@temptoken=\toks28
\footnotemargin=\dimen180
\@outputbox@depth=\dimen181
Package footmisc Info: Declaring symbol style bringhurst on input line 699.
Package footmisc Info: Declaring symbol style chicago on input line 707.
Package footmisc Info: Declaring symbol style wiley on input line 716.
Package footmisc Info: Declaring symbol style lamport-robust on input line 727.
Package footmisc Info: Declaring symbol style lamport* on input line 747.
Package footmisc Info: Declaring symbol style lamport*-robust on input line 768.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/newtxtext.sty
Package: newtxtext 2024/04/01 v1.744(Michael Sharpe) latex and unicode latex support for TeXGyreTermesX
 `newtxtext' v1.744, 2024/04/01 Text macros taking advantage of TeXGyre Termes and its extensions (msharpe) (/usr/local/texlive/2025/texmf-dist/tex/latex/xpatch/xpatch.sty (/usr/local/texlive/2025/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-01-18 L3 programming layer (loader) 
 (/usr/local/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count303
\l__pdf_internal_box=\box101
))
Package: xpatch 2020/03/25 v0.3a Extending etoolbox patching commands
 (/usr/local/texlive/2025/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-08-16 L3 Experimental document command parser
) (/usr/local/texlive/2025/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count304
)) (/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
) (/usr/local/texlive/2025/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/xkeyval/xkeyval.tex (/usr/local/texlive/2025/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks29
\XKV@tempa@toks=\toks30
)
\XKV@depth=\count305
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
) (/usr/local/texlive/2025/texmf-dist/tex/generic/xstring/xstring.sty (/usr/local/texlive/2025/texmf-dist/tex/generic/xstring/xstring.tex
\xs_counta=\count306
\xs_countb=\count307
)
Package: xstring 2023/08/22 v1.86 String manipulations (CT)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/carlisle/scalefnt.sty)
LaTeX Font Info:    Setting ntxLF sub-encoding to TS1/0 on input line 24.
LaTeX Font Info:    Setting ntxTLF sub-encoding to TS1/0 on input line 24.
LaTeX Font Info:    Setting ntxOsF sub-encoding to TS1/0 on input line 24.
LaTeX Font Info:    Setting ntxTOsF sub-encoding to TS1/0 on input line 24.
 (/usr/local/texlive/2025/texmf-dist/tex/generic/kastrup/binhex.tex)
\ntx@tmpcnta=\count308
\ntx@cnt=\count309
 (/usr/local/texlive/2025/texmf-dist/tex/latex/fontaxes/fontaxes.sty
Package: fontaxes 2020/07/21 v1.0e Font selection axes
LaTeX Info: Redefining \upshape on input line 29.
LaTeX Info: Redefining \itshape on input line 31.
LaTeX Info: Redefining \slshape on input line 33.
LaTeX Info: Redefining \swshape on input line 35.
LaTeX Info: Redefining \scshape on input line 37.
LaTeX Info: Redefining \sscshape on input line 39.
LaTeX Info: Redefining \ulcshape on input line 41.
LaTeX Info: Redefining \textsw on input line 47.
LaTeX Info: Redefining \textssc on input line 48.
LaTeX Info: Redefining \textulc on input line 49.
)
\tx@sixem=\dimen182
\tx@y=\dimen183
\tx@x=\dimen184
\tx@tmpdima=\dimen185
\tx@tmpdimb=\dimen186
\tx@tmpdimc=\dimen187
\tx@tmpdimd=\dimen188
\tx@tmpdime=\dimen189
\tx@tmpdimf=\dimen190
\tx@dimA=\dimen191
\tx@dimAA=\dimen192
\tx@dimB=\dimen193
\tx@dimBB=\dimen194
\tx@dimC=\dimen195
LaTeX Info: Redefining \oldstylenums on input line 902.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/newtxmath.sty
Package: newtxmath 2024/09/22 v1.754
 `newtxmath' v1.754, 2024/09/22 Math macros based originally on txfonts (msharpe) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip57

For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks31
\ex@=\dimen196
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen197
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count310
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count311
\leftroot@=\count312
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count313
\DOTSCASE@=\count314
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box102
\strutbox@=\box103
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen198
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count315
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count316
\dotsspace@=\muskip17
\c@parentequation=\count317
\dspbrk@lvl=\count318
\tag@help=\toks32
\row@=\count319
\column@=\count320
\maxfields@=\count321
\andhelp@=\toks33
\eqnshift@=\dimen199
\alignsep@=\dimen256
\tagshift@=\dimen257
\tagwidth@=\dimen258
\totwidth@=\dimen259
\lineht@=\dimen260
\@envbody=\toks34
\multlinegap=\skip58
\multlinetaggap=\skip59
\mathdisplay@stack=\toks35
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/oberdiek/centernot.sty
Package: centernot 2016/05/16 v1.4 Centers the not symbol horizontally (HO)
)
\tx@cntz=\count322
 (/usr/local/texlive/2025/texmf-dist/tex/generic/kastrup/binhex.tex)
\tx@Isdigit=\count323
\tx@IsAlNum=\count324
\tx@tA=\toks36
\tx@tB=\toks37
\tx@su=\read2

amsthm NOT loaded
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 402.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/minntx/m/n on input line 402.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/minntx/m/n on input line 402.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/minntx/m/n --> OT1/minntx/b/n on input line 403.
LaTeX Font Info:    Redeclaring math alphabet \mathsf on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> OT1/qhv/m/n on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> OT1/qhv/m/n on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/qhv/m/n --> OT1/qhv/b/n on input line 412.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 419.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/minntx/m/it on input line 419.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/minntx/m/it on input line 419.
LaTeX Font Info:    Redeclaring math alphabet \mathtt on input line 420.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> OT1/ntxtt/m/n on input line 420.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> OT1/ntxtt/m/n on input line 420.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 422.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/minntx/b/n on input line 422.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/minntx/b/n on input line 422.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/minntx/m/it --> OT1/minntx/b/it on input line 423.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/ntxtt/m/n --> OT1/ntxtt/b/n on input line 426.
LaTeX Font Info:    Redeclaring symbol font `letters' on input line 534.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/ntxmi/m/it on input line 534.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/ntxmi/m/it on input line 534.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/ntxmi/m/it --> OML/ntxmi/b/it on input line 535.
\symlettersA=\mathgroup4
LaTeX Font Info:    Overwriting symbol font `lettersA' in version `bold'
(Font)                  U/ntxmia/m/it --> U/ntxmia/b/it on input line 582.
Now handling font encoding LMS ...
... no UTF-8 mapping file for font encoding LMS
LaTeX Font Info:    Redeclaring symbol font `symbols' on input line 604.
LaTeX Font Info:    Encoding `OMS' has changed to `LMS' for symbol font
(Font)              `symbols' in the math version `normal' on input line 604.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> LMS/ntxsy/m/n on input line 604.
LaTeX Font Info:    Encoding `OMS' has changed to `LMS' for symbol font
(Font)              `symbols' in the math version `bold' on input line 604.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> LMS/ntxsy/m/n on input line 604.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  LMS/ntxsy/m/n --> LMS/ntxsy/b/n on input line 605.
\symAMSm=\mathgroup5
LaTeX Font Info:    Overwriting symbol font `AMSm' in version `bold'
(Font)                  U/ntxsym/m/n --> U/ntxsym/b/n on input line 630.
\symsymbolsC=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `symbolsC' in version `bold'
(Font)                  U/ntxsyc/m/n --> U/ntxsyc/b/n on input line 651.
Now handling font encoding LMX ...
... no UTF-8 mapping file for font encoding LMX
LaTeX Font Info:    Redeclaring symbol font `largesymbols' on input line 664.
LaTeX Font Info:    Encoding `OMX' has changed to `LMX' for symbol font
(Font)              `largesymbols' in the math version `normal' on input line 664.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> LMX/ntxexx/m/n on input line 664.
LaTeX Font Info:    Encoding `OMX' has changed to `LMX' for symbol font
(Font)              `largesymbols' in the math version `bold' on input line 664.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> LMX/ntxexx/m/n on input line 664.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  LMX/ntxexx/m/n --> LMX/ntxexx/b/n on input line 665.
\symlargesymbolsTXA=\mathgroup7
LaTeX Font Info:    Overwriting symbol font `largesymbolsTXA' in version `bold'
(Font)                  U/ntxexa/m/n --> U/ntxexa/b/n on input line 679.
\tx@sbptoks=\toks38
LaTeX Font Info:    Redeclaring math delimiter \lfloor on input line 902.
LaTeX Font Info:    Redeclaring math delimiter \rfloor on input line 903.
LaTeX Font Info:    Redeclaring math delimiter \lceil on input line 904.
LaTeX Font Info:    Redeclaring math delimiter \rceil on input line 905.
LaTeX Font Info:    Redeclaring math delimiter \lbrace on input line 910.
LaTeX Font Info:    Redeclaring math delimiter \rbrace on input line 911.
LaTeX Font Info:    Redeclaring math delimiter \langle on input line 913.
LaTeX Font Info:    Redeclaring math delimiter \rangle on input line 915.
LaTeX Font Info:    Redeclaring math delimiter \arrowvert on input line 919.
LaTeX Font Info:    Redeclaring math delimiter \vert on input line 920.
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 966.
LaTeX Font Info:    Redeclaring math accent \dot on input line 991.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 992.
LaTeX Font Info:    Redeclaring math accent \vec on input line 2057.
LaTeX Info: Redefining \Bbbk on input line 2847.
LaTeX Info: Redefining \not on input line 2995.
LaTeX Info: Redefining \textsquare on input line 3025.
LaTeX Info: Redefining \openbox on input line 3027.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/pdfpages/pdfpages.sty
Package: pdfpages 2025/01/30 v0.6e Insert pages of external PDF documents (AM)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2023/07/08 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count325
\calc@Bcount=\count326
\calc@Adimen=\dimen261
\calc@Bdimen=\dimen262
\calc@Askip=\skip60
\calc@Bskip=\skip61
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count327
\calc@Cskip=\skip62
) (/usr/local/texlive/2025/texmf-dist/tex/latex/eso-pic/eso-pic.sty
Package: eso-pic 2023/05/03 v3.0c eso-pic (RN)
\ESO@tempdima=\dimen263
\ESO@tempdimb=\dimen264
)
\AM@pagewidth=\dimen265
\AM@pageheight=\dimen266
\AM@fboxrule=\dimen267
 (/usr/local/texlive/2025/texmf-dist/tex/latex/pdfpages/pppdftex.def
File: pppdftex.def 2025/01/30 v0.6e Pdfpages driver for pdfTeX (AM)
)
\pdfpages@includegraphics@status=\count328
\AM@pagebox=\box104
\AM@global@opts=\toks39
\AM@pagecnt=\count329
\AM@toc@title=\toks40
\AM@lof@heading=\toks41
\c@AM@survey=\count330
\AM@templatesizebox=\box105
)

LaTeX Warning: \include should only be used after \begin{document} on input line 39.

No file editor/preambleWAD.
(/usr/local/texlive/2025/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen268
\lightrulewidth=\dimen269
\cmidrulewidth=\dimen270
\belowrulesep=\dimen271
\belowbottomsep=\dimen272
\aboverulesep=\dimen273
\abovetopsep=\dimen274
\cmidrulesep=\dimen275
\cmidrulekern=\dimen276
\defaultaddspace=\dimen277
\@cmidla=\count331
\@cmidlb=\count332
\@aboverulesep=\dimen278
\@belowrulesep=\dimen279
\@thisruleclass=\count333
\@lastruleclass=\count334
\@thisrulewidth=\dimen280
) (/usr/local/texlive/2025/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip63
\multirow@cntb=\count335
\multirow@dima=\skip64
\bigstrutjot=\dimen281
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup8
\symAMSb=\mathgroup9
LaTeX Font Info:    Redeclaring math delimiter \ulcorner on input line 74.
LaTeX Font Info:    Redeclaring math delimiter \urcorner on input line 75.
LaTeX Font Info:    Redeclaring math delimiter \llcorner on input line 76.
LaTeX Font Info:    Redeclaring math delimiter \lrcorner on input line 77.
LaTeX Font Info:    Redeclaring math symbol \square on input line 141.
LaTeX Font Info:    Redeclaring math symbol \lozenge on input line 142.
)
LaTeX Font Info:    Redeclaring math symbol \boxdot on input line 44.
LaTeX Font Info:    Redeclaring math symbol \boxplus on input line 45.
LaTeX Font Info:    Redeclaring math symbol \boxtimes on input line 46.
LaTeX Font Info:    Redeclaring math symbol \blacksquare on input line 48.
LaTeX Font Info:    Redeclaring math symbol \centerdot on input line 49.
LaTeX Font Info:    Redeclaring math symbol \blacklozenge on input line 51.
LaTeX Font Info:    Redeclaring math symbol \circlearrowright on input line 52.
LaTeX Font Info:    Redeclaring math symbol \circlearrowleft on input line 53.
LaTeX Font Info:    Redeclaring math symbol \leftrightharpoons on input line 56.
LaTeX Font Info:    Redeclaring math symbol \boxminus on input line 57.
LaTeX Font Info:    Redeclaring math symbol \Vdash on input line 58.
LaTeX Font Info:    Redeclaring math symbol \Vvdash on input line 59.
LaTeX Font Info:    Redeclaring math symbol \vDash on input line 60.
LaTeX Font Info:    Redeclaring math symbol \twoheadrightarrow on input line 61.
LaTeX Font Info:    Redeclaring math symbol \twoheadleftarrow on input line 62.
LaTeX Font Info:    Redeclaring math symbol \leftleftarrows on input line 63.
LaTeX Font Info:    Redeclaring math symbol \rightrightarrows on input line 64.
LaTeX Font Info:    Redeclaring math symbol \upuparrows on input line 65.
LaTeX Font Info:    Redeclaring math symbol \downdownarrows on input line 66.
LaTeX Font Info:    Redeclaring math symbol \upharpoonright on input line 67.
LaTeX Font Info:    Redeclaring math symbol \downharpoonright on input line 69.
LaTeX Font Info:    Redeclaring math symbol \upharpoonleft on input line 70.
LaTeX Font Info:    Redeclaring math symbol \downharpoonleft on input line 71.
LaTeX Font Info:    Redeclaring math symbol \rightarrowtail on input line 72.
LaTeX Font Info:    Redeclaring math symbol \leftarrowtail on input line 73.
LaTeX Font Info:    Redeclaring math symbol \leftrightarrows on input line 74.
LaTeX Font Info:    Redeclaring math symbol \rightleftarrows on input line 75.
LaTeX Font Info:    Redeclaring math symbol \Lsh on input line 76.
LaTeX Font Info:    Redeclaring math symbol \Rsh on input line 77.
LaTeX Font Info:    Redeclaring math symbol \leftrightsquigarrow on input line 79.
LaTeX Font Info:    Redeclaring math symbol \looparrowleft on input line 80.
LaTeX Font Info:    Redeclaring math symbol \looparrowright on input line 81.
LaTeX Font Info:    Redeclaring math symbol \circeq on input line 82.
LaTeX Font Info:    Redeclaring math symbol \succsim on input line 83.
LaTeX Font Info:    Redeclaring math symbol \gtrsim on input line 84.
LaTeX Font Info:    Redeclaring math symbol \gtrapprox on input line 85.
LaTeX Font Info:    Redeclaring math symbol \multimap on input line 86.
LaTeX Font Info:    Redeclaring math symbol \therefore on input line 87.
LaTeX Font Info:    Redeclaring math symbol \because on input line 88.
LaTeX Font Info:    Redeclaring math symbol \doteqdot on input line 89.
LaTeX Font Info:    Redeclaring math symbol \triangleq on input line 91.
LaTeX Font Info:    Redeclaring math symbol \precsim on input line 92.
LaTeX Font Info:    Redeclaring math symbol \lesssim on input line 93.
LaTeX Font Info:    Redeclaring math symbol \lessapprox on input line 94.
LaTeX Font Info:    Redeclaring math symbol \eqslantless on input line 95.
LaTeX Font Info:    Redeclaring math symbol \eqslantgtr on input line 96.
LaTeX Font Info:    Redeclaring math symbol \curlyeqprec on input line 97.
LaTeX Font Info:    Redeclaring math symbol \curlyeqsucc on input line 98.
LaTeX Font Info:    Redeclaring math symbol \preccurlyeq on input line 99.
LaTeX Font Info:    Redeclaring math symbol \leqq on input line 100.
LaTeX Font Info:    Redeclaring math symbol \leqslant on input line 101.
LaTeX Font Info:    Redeclaring math symbol \lessgtr on input line 102.
LaTeX Font Info:    Redeclaring math symbol \backprime on input line 103.
LaTeX Font Info:    Redeclaring math symbol \risingdotseq on input line 104.
LaTeX Font Info:    Redeclaring math symbol \fallingdotseq on input line 105.
LaTeX Font Info:    Redeclaring math symbol \succcurlyeq on input line 106.
LaTeX Font Info:    Redeclaring math symbol \geqq on input line 107.
LaTeX Font Info:    Redeclaring math symbol \geqslant on input line 108.
LaTeX Font Info:    Redeclaring math symbol \gtrless on input line 109.
LaTeX Font Info:    Redeclaring math symbol \bigstar on input line 117.
LaTeX Font Info:    Redeclaring math symbol \between on input line 118.
LaTeX Font Info:    Redeclaring math symbol \blacktriangledown on input line 119.
LaTeX Font Info:    Redeclaring math symbol \blacktriangleright on input line 120.
LaTeX Font Info:    Redeclaring math symbol \blacktriangleleft on input line 121.
LaTeX Font Info:    Redeclaring math symbol \vartriangle on input line 122.
LaTeX Font Info:    Redeclaring math symbol \blacktriangle on input line 123.
LaTeX Font Info:    Redeclaring math symbol \triangledown on input line 124.
LaTeX Font Info:    Redeclaring math symbol \eqcirc on input line 125.
LaTeX Font Info:    Redeclaring math symbol \lesseqgtr on input line 126.
LaTeX Font Info:    Redeclaring math symbol \gtreqless on input line 127.
LaTeX Font Info:    Redeclaring math symbol \lesseqqgtr on input line 128.
LaTeX Font Info:    Redeclaring math symbol \gtreqqless on input line 129.
LaTeX Font Info:    Redeclaring math symbol \Rrightarrow on input line 130.
LaTeX Font Info:    Redeclaring math symbol \Lleftarrow on input line 131.
LaTeX Font Info:    Redeclaring math symbol \veebar on input line 132.
LaTeX Font Info:    Redeclaring math symbol \barwedge on input line 133.
LaTeX Font Info:    Redeclaring math symbol \doublebarwedge on input line 134.
LaTeX Font Info:    Redeclaring math symbol \measuredangle on input line 137.
LaTeX Font Info:    Redeclaring math symbol \sphericalangle on input line 138.
LaTeX Font Info:    Redeclaring math symbol \varpropto on input line 139.
LaTeX Font Info:    Redeclaring math symbol \smallsmile on input line 140.
LaTeX Font Info:    Redeclaring math symbol \smallfrown on input line 141.
LaTeX Font Info:    Redeclaring math symbol \Subset on input line 142.
LaTeX Font Info:    Redeclaring math symbol \Supset on input line 143.
LaTeX Font Info:    Redeclaring math symbol \Cup on input line 144.
LaTeX Font Info:    Redeclaring math symbol \Cap on input line 146.
LaTeX Font Info:    Redeclaring math symbol \curlywedge on input line 148.
LaTeX Font Info:    Redeclaring math symbol \curlyvee on input line 149.
LaTeX Font Info:    Redeclaring math symbol \leftthreetimes on input line 150.
LaTeX Font Info:    Redeclaring math symbol \rightthreetimes on input line 151.
LaTeX Font Info:    Redeclaring math symbol \subseteqq on input line 152.
LaTeX Font Info:    Redeclaring math symbol \supseteqq on input line 153.
LaTeX Font Info:    Redeclaring math symbol \bumpeq on input line 154.
LaTeX Font Info:    Redeclaring math symbol \Bumpeq on input line 155.
LaTeX Font Info:    Redeclaring math symbol \lll on input line 156.
LaTeX Font Info:    Redeclaring math symbol \ggg on input line 158.
LaTeX Font Info:    Redeclaring math symbol \circledS on input line 160.
LaTeX Font Info:    Redeclaring math symbol \pitchfork on input line 161.
LaTeX Font Info:    Redeclaring math symbol \dotplus on input line 162.
LaTeX Font Info:    Redeclaring math symbol \backsim on input line 163.
LaTeX Font Info:    Redeclaring math symbol \backsimeq on input line 164.
LaTeX Font Info:    Redeclaring math symbol \complement on input line 165.
LaTeX Font Info:    Redeclaring math symbol \intercal on input line 166.
LaTeX Font Info:    Redeclaring math symbol \circledcirc on input line 167.
LaTeX Font Info:    Redeclaring math symbol \circledast on input line 168.
LaTeX Font Info:    Redeclaring math symbol \circleddash on input line 169.
LaTeX Font Info:    Redeclaring math symbol \lvertneqq on input line 171.
LaTeX Font Info:    Redeclaring math symbol \gvertneqq on input line 172.
LaTeX Font Info:    Redeclaring math symbol \nleq on input line 173.
LaTeX Font Info:    Redeclaring math symbol \ngeq on input line 174.
LaTeX Font Info:    Redeclaring math symbol \nless on input line 175.
LaTeX Font Info:    Redeclaring math symbol \ngtr on input line 176.
LaTeX Font Info:    Redeclaring math symbol \nprec on input line 177.
LaTeX Font Info:    Redeclaring math symbol \nsucc on input line 178.
LaTeX Font Info:    Redeclaring math symbol \lneqq on input line 179.
LaTeX Font Info:    Redeclaring math symbol \gneqq on input line 180.
LaTeX Font Info:    Redeclaring math symbol \nleqslant on input line 181.
LaTeX Font Info:    Redeclaring math symbol \ngeqslant on input line 182.
LaTeX Font Info:    Redeclaring math symbol \lneq on input line 183.
LaTeX Font Info:    Redeclaring math symbol \gneq on input line 184.
LaTeX Font Info:    Redeclaring math symbol \npreceq on input line 185.
LaTeX Font Info:    Redeclaring math symbol \nsucceq on input line 186.
LaTeX Font Info:    Redeclaring math symbol \precnsim on input line 187.
LaTeX Font Info:    Redeclaring math symbol \succnsim on input line 188.
LaTeX Font Info:    Redeclaring math symbol \lnsim on input line 189.
LaTeX Font Info:    Redeclaring math symbol \gnsim on input line 190.
LaTeX Font Info:    Redeclaring math symbol \nleqq on input line 191.
LaTeX Font Info:    Redeclaring math symbol \ngeqq on input line 192.
LaTeX Font Info:    Redeclaring math symbol \precneqq on input line 193.
LaTeX Font Info:    Redeclaring math symbol \succneqq on input line 194.
LaTeX Font Info:    Redeclaring math symbol \precnapprox on input line 195.
LaTeX Font Info:    Redeclaring math symbol \succnapprox on input line 196.
LaTeX Font Info:    Redeclaring math symbol \lnapprox on input line 197.
LaTeX Font Info:    Redeclaring math symbol \gnapprox on input line 198.
LaTeX Font Info:    Redeclaring math symbol \nsim on input line 199.
LaTeX Font Info:    Redeclaring math symbol \ncong on input line 200.
LaTeX Font Info:    Redeclaring math symbol \diagup on input line 201.
LaTeX Font Info:    Redeclaring math symbol \diagdown on input line 202.
LaTeX Font Info:    Redeclaring math symbol \varsubsetneq on input line 203.
LaTeX Font Info:    Redeclaring math symbol \varsupsetneq on input line 204.
LaTeX Font Info:    Redeclaring math symbol \nsubseteqq on input line 205.
LaTeX Font Info:    Redeclaring math symbol \nsupseteqq on input line 206.
LaTeX Font Info:    Redeclaring math symbol \subsetneqq on input line 207.
LaTeX Font Info:    Redeclaring math symbol \supsetneqq on input line 208.
LaTeX Font Info:    Redeclaring math symbol \varsubsetneqq on input line 209.
LaTeX Font Info:    Redeclaring math symbol \varsupsetneqq on input line 210.
LaTeX Font Info:    Redeclaring math symbol \subsetneq on input line 211.
LaTeX Font Info:    Redeclaring math symbol \supsetneq on input line 212.
LaTeX Font Info:    Redeclaring math symbol \nsubseteq on input line 213.
LaTeX Font Info:    Redeclaring math symbol \nsupseteq on input line 214.
LaTeX Font Info:    Redeclaring math symbol \nparallel on input line 215.
LaTeX Font Info:    Redeclaring math symbol \nmid on input line 216.
LaTeX Font Info:    Redeclaring math symbol \nshortmid on input line 217.
LaTeX Font Info:    Redeclaring math symbol \nshortparallel on input line 218.
LaTeX Font Info:    Redeclaring math symbol \nvdash on input line 219.
LaTeX Font Info:    Redeclaring math symbol \nVdash on input line 220.
LaTeX Font Info:    Redeclaring math symbol \nvDash on input line 221.
LaTeX Font Info:    Redeclaring math symbol \nVDash on input line 222.
LaTeX Font Info:    Redeclaring math symbol \ntrianglerighteq on input line 223.
LaTeX Font Info:    Redeclaring math symbol \ntrianglelefteq on input line 224.
LaTeX Font Info:    Redeclaring math symbol \ntriangleleft on input line 225.
LaTeX Font Info:    Redeclaring math symbol \ntriangleright on input line 226.
LaTeX Font Info:    Redeclaring math symbol \nleftarrow on input line 227.
LaTeX Font Info:    Redeclaring math symbol \nrightarrow on input line 228.
LaTeX Font Info:    Redeclaring math symbol \nLeftarrow on input line 229.
LaTeX Font Info:    Redeclaring math symbol \nRightarrow on input line 230.
LaTeX Font Info:    Redeclaring math symbol \nLeftrightarrow on input line 231.
LaTeX Font Info:    Redeclaring math symbol \nleftrightarrow on input line 232.
LaTeX Font Info:    Redeclaring math symbol \divideontimes on input line 233.
LaTeX Font Info:    Redeclaring math symbol \varnothing on input line 234.
LaTeX Font Info:    Redeclaring math symbol \nexists on input line 235.
LaTeX Font Info:    Redeclaring math symbol \Finv on input line 236.
LaTeX Font Info:    Redeclaring math symbol \Game on input line 237.
LaTeX Font Info:    Redeclaring math symbol \eth on input line 240.
LaTeX Font Info:    Redeclaring math symbol \eqsim on input line 241.
LaTeX Font Info:    Redeclaring math symbol \beth on input line 242.
LaTeX Font Info:    Redeclaring math symbol \gimel on input line 243.
LaTeX Font Info:    Redeclaring math symbol \daleth on input line 244.
LaTeX Font Info:    Redeclaring math symbol \lessdot on input line 245.
LaTeX Font Info:    Redeclaring math symbol \gtrdot on input line 246.
LaTeX Font Info:    Redeclaring math symbol \ltimes on input line 247.
LaTeX Font Info:    Redeclaring math symbol \rtimes on input line 248.
LaTeX Font Info:    Redeclaring math symbol \shortmid on input line 249.
LaTeX Font Info:    Redeclaring math symbol \shortparallel on input line 250.
LaTeX Font Info:    Redeclaring math symbol \smallsetminus on input line 251.
LaTeX Font Info:    Redeclaring math symbol \thicksim on input line 252.
LaTeX Font Info:    Redeclaring math symbol \thickapprox on input line 253.
LaTeX Font Info:    Redeclaring math symbol \approxeq on input line 254.
LaTeX Font Info:    Redeclaring math symbol \succapprox on input line 255.
LaTeX Font Info:    Redeclaring math symbol \precapprox on input line 256.
LaTeX Font Info:    Redeclaring math symbol \curvearrowleft on input line 257.
LaTeX Font Info:    Redeclaring math symbol \curvearrowright on input line 258.
LaTeX Font Info:    Redeclaring math symbol \digamma on input line 259.
LaTeX Font Info:    Redeclaring math symbol \varkappa on input line 260.


/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty:261: LaTeX Error: Command `\Bbbk' already defined.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.261 ...ol{\Bbbk}           {\mathord}{AMSb}{"7C}
                                                  
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

LaTeX Font Info:    Redeclaring math symbol \hslash on input line 262.
LaTeX Font Info:    Redeclaring math symbol \backepsilon on input line 265.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/appendix/appendix.sty
Package: appendix 2020/02/08 v1.2c extra appendix facilities
\c@@pps=\count336
\c@@ppsavesec=\count337
\c@@ppsaveapp=\count338
) (/usr/local/texlive/2025/texmf-dist/tex/latex/ncctools/manyfoot.sty
Package: manyfoot 2019/08/03 v1.11 Many Footnote Levels Package (NCC)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/ncctools/nccfoots.sty
Package: nccfoots 2005/02/03 v1.2 NCC Footnotes Package (NCC)
)
\MFL@columnwidth=\dimen282
) (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/array.sty
Package: array 2024/10/17 v2.6g Tabular extension package (FMi)
\col@sep=\dimen283
\ar@mcellbox=\box106
\extrarowheight=\dimen284
\NC@list=\toks42
\extratabsurround=\skip65
\backup@length=\skip66
\ar@cellbox=\box107
) (/usr/local/texlive/2025/texmf-dist/tex/latex/algorithms/algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
 (/usr/local/texlive/2025/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count339
\float@exts=\toks43
\float@box=\box108
\@float@everytoks=\toks44
\@floatcapt=\box109
)
\@float@every@algorithm=\toks45
\c@algorithm=\count340
) (/usr/local/texlive/2025/texmf-dist/tex/latex/algorithmicx/algorithmicx.sty
Package: algorithmicx 2005/04/27 v1.2 Algorithmicx

Document Style algorithmicx 1.2 - a greatly improved `algorithmic' style
\c@ALG@line=\count341
\c@ALG@rem=\count342
\c@ALG@nested=\count343
\ALG@tlm=\skip67
\ALG@thistlm=\skip68
\c@ALG@Lnr=\count344
\c@ALG@blocknr=\count345
\c@ALG@storecount=\count346
\c@ALG@tmpcounter=\count347
\ALG@tmplength=\skip69
) (/usr/local/texlive/2025/texmf-dist/tex/latex/algorithmicx/algpseudocode.sty
Package: algpseudocode 

Document Style - pseudocode environments for use with the `algorithmicx' style
) (/usr/local/texlive/2025/texmf-dist/tex/latex/listings/listings.sty
\lst@mode=\count348
\lst@gtempboxa=\box110
\lst@token=\toks46
\lst@length=\count349
\lst@currlwidth=\dimen285
\lst@column=\count350
\lst@pos=\count351
\lst@lostspace=\dimen286
\lst@width=\dimen287
\lst@newlines=\count352
\lst@lineno=\count353
\lst@maxwidth=\dimen288
 (/usr/local/texlive/2025/texmf-dist/tex/latex/listings/lstpatch.sty
File: lstpatch.sty 2024/09/23 1.10c (Carsten Heinz)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/listings/lstmisc.sty
File: lstmisc.sty 2024/09/23 1.10c (Carsten Heinz)
\c@lstnumber=\count354
\lst@skipnumbers=\count355
\lst@framebox=\box111
) (/usr/local/texlive/2025/texmf-dist/tex/latex/listings/listings.cfg
File: listings.cfg 2024/09/23 1.10c listings configuration
))
Package: listings 2024/09/23 1.10c (Carsten Heinz)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/euscript.sty
Package: euscript 2009/06/22 v3.00 Euler Script fonts
LaTeX Font Info:    Overwriting math alphabet `\EuScript' in version `bold'
(Font)                  U/eus/m/n --> U/eus/b/n on input line 33.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/tabularx.sty
Package: tabularx 2023/12/11 v2.12a `tabularx' package (DPC)
\TX@col@width=\dimen289
\TX@old@table=\dimen290
\TX@old@col=\dimen291
\TX@target=\dimen292
\TX@delta=\dimen293
\TX@cols=\count356
\TX@ftn=\toks47
) (/usr/local/texlive/2025/texmf-dist/tex/latex/colortbl/colortbl.sty
Package: colortbl 2024/07/06 v1.0i Color table columns (DPC)
\everycr=\toks48
\minrowclearance=\skip70
\rownum=\count357
)
\@indexfile=\write4
\openout4 = `editor.idx'.


Writing index file editor.idx
LaTeX Font Info:    Trying to load font information for OT1+ntxtlf on input line 71.
(/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/ot1ntxtlf.fd
File: ot1ntxtlf.fd 2021/05/24 v1.0 font definition file for OT1/ntx/tlf
)
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 10.0pt on input line 71.

No file editor.aux.
\openout1 = `editor.aux'.

LaTeX Font Info:    Checking defaults for OML/ntxmi/m/it on input line 71.
LaTeX Font Info:    Trying to load font information for OML+ntxmi on input line 71.
(/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/omlntxmi.fd
File: omlntxmi.fd 2015/08/25 Fontinst v1.933 font definitions for OML/ntxmi.
)
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 71.
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for U/ntxexa/m/n on input line 71.
LaTeX Font Info:    Trying to load font information for U+ntxexa on input line 71.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/untxexa.fd
File: untxexa.fd 2012/04/16 Fontinst v1.933 font definitions for U/ntxexa.
)
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for LMS/ntxsy/m/n on input line 71.
LaTeX Font Info:    Trying to load font information for LMS+ntxsy on input line 71.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/lmsntxsy.fd
File: lmsntxsy.fd 2016/07/02 Fontinst v1.933 font definitions for LMS/ntxsy.
)
LaTeX Font Info:    ... okay on input line 71.
LaTeX Font Info:    Checking defaults for LMX/ntxexx/m/n on input line 71.
LaTeX Font Info:    Trying to load font information for LMX+ntxexx on input line 71.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/lmxntxexx.fd
File: lmxntxexx.fd 2016/07/03 Fontinst v1.933 font definitions for LMX/ntxexx.
)
LaTeX Font Info:    ... okay on input line 71.
 (/usr/local/texlive/2025/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count358
\scratchdimen=\dimen294
\scratchbox=\box112
\nofMPsegments=\count359
\nofMParguments=\count360
\everyMPshowfont=\toks49
\MPscratchCnt=\count361
\MPscratchDim=\dimen295
\MPnumerator=\count362
\makeMPintoPDFobject=\count363
\everyMPtoPDFconversion=\toks50
) (/usr/local/texlive/2025/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
\c@mv@tabular=\count364
\c@mv@boldtabular=\count365
LaTeX Info: Command `\dddot' is already robust on input line 71.
LaTeX Info: Command `\ddddot' is already robust on input line 71.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/pdflscape/pdflscape.sty
Package: pdflscape 2022-10-27 v0.13 Display of landscape pages in PDF
 (/usr/local/texlive/2025/texmf-dist/tex/latex/pdflscape/pdflscape-nometadata.sty
Package: pdflscape-nometadata 2022-10-28 v0.13 Display of landscape pages in PDF (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/lscape.sty
Package: lscape 2020/05/28 v3.02 Landscape Pages (DPC)
)
Package pdflscape Info: Auto-detected driver: pdftex on input line 81.
))
\c@lstlisting=\count366
\openout2 = `dedication.aux'.

 (./dedication.tex
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 12.0pt on input line 9.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/it' will be
(Font)              scaled to size 12.0pt on input line 9.


[5






{/usr/local/texlive/2025/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{/usr/local/texlive/2025/texmf-dist/fonts/enc/dvips/newtx/ntx-ot1-tlf.enc}])
\openout2 = `foreword.aux'.

 (./foreword.tex

[6






]
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 16.0pt on input line 9.
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 16.0pt on input line 9.
LaTeX Font Info:    Trying to load font information for OT1+minntx on input line 9.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/ot1minntx.fd
File: ot1minntx.fd 2023/09/09 v1.1 font definition file for OT1/minntx
)
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Trying to load font information for U+ntxmia on input line 9.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/untxmia.fd
File: untxmia.fd 2024/04/09 Fontinst v1.933 font definitions for U/ntxmia.
)
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Trying to load font information for U+ntxsym on input line 9.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/untxsym.fd
File: untxsym.fd 2023/08/16 Fontinst v1.933 font definitions for U/ntxsym.
)
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Trying to load font information for U+ntxsyc on input line 9.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/untxsyc.fd
File: untxsyc.fd 2012/04/12 Fontinst v1.933 font definitions for U/ntxsyc.
)
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 10.0pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 7.3pt on input line 9.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 5.5pt on input line 9.
LaTeX Font Info:    Trying to load font information for U+msa on input line 9.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 9.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/it' will be
(Font)              scaled to size 10.0pt on input line 10.

Overfull \hbox (3.95215pt too wide) in paragraph at lines 10--11
\OT1/minntx/m/n/10 Use the tem-plate \OT1/ntxtlf/m/it/10 fore-word.tex \OT1/minntx/m/n/10 to-gether with the doc-u-ment class SV-Mono (monograph-
 []

)

LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 8.5pt on input line 76.
[7]
\openout2 = `preface.aux'.

 (./preface.tex

[8





])

[9]
\openout2 = `acknowledgement.aux'.

 (./acknowledgement.tex

[10





])

[11]

[12




]
No file editor.toc.
\tf@toc=\write5
\openout5 = `editor.toc'.



[13]
\openout2 = `contriblist.aux'.

 (./contriblist.tex

[14



])

[15]
\openout2 = `acronym.aux'.

 (./acronym.tex

[16





]
Overfull \hbox (3.22217pt too wide) in paragraph at lines 10--11
\OT1/minntx/m/n/10 Use the tem-plate \OT1/ntxtlf/m/it/10 acronym.tex \OT1/minntx/m/n/10 to-gether with the doc-u-ment class SV-Mono (monograph-
 []

LaTeX Font Info:    Trying to load font information for OT1+ntxtt on input line 12.
(/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/ot1ntxtt.fd
File: ot1ntxtt.fd 2012/04/20 v3.1
)
LaTeX Font Info:    Font shape `OT1/ntxtt/m/n' will be
(Font)              scaled to size 10.0pt on input line 12.
)

[17]

[18




]
\openout2 = `part01.aux'.

 (./part01.tex
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 18.0pt on input line 10.


[1



]

[2])
\openout2 = `author001.aux'.

 (./author001.tex
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 14.0pt on input line 71.
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 14.0pt on input line 71.
LaTeX Font Info:    Calculating math sizes for size <8.5> on input line 71.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 8.5pt on input line 71.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 6.20496pt on input line 71.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 4.67502pt on input line 71.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 8.5pt on input line 71.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 6.20496pt on input line 71.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 4.67502pt on input line 71.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 8.5pt on input line 71.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 6.20496pt on input line 71.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 4.67502pt on input line 71.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 8.5pt on input line 71.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 6.20496pt on input line 71.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 4.67502pt on input line 71.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 8.5pt on input line 71.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 6.20496pt on input line 71.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 4.67502pt on input line 71.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 8.5pt on input line 71.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 6.20496pt on input line 71.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 4.67502pt on input line 71.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 8.5pt on input line 71.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 6.20496pt on input line 71.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 4.67502pt on input line 71.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 8.5pt on input line 71.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 6.20496pt on input line 71.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 4.67502pt on input line 71.

Title too long for running head. Please supply
a shorter form with \titlerunning prior to \maketitle
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 10.0pt on input line 77.

LaTeX Warning: Citation `stoudenmire2016supervised' on page 3 undefined on input line 77.

LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 12.0pt on input line 79.

LaTeX Warning: Reference `fig:TN' on page 3 undefined on input line 80.


LaTeX Warning: Citation `wang2023tensor' on page 3 undefined on input line 81.


LaTeX Warning: Citation `novikov2015tensorizing' on page 3 undefined on input line 81.


LaTeX Warning: Citation `stoudenmire2016supervised' on page 3 undefined on input line 81.


LaTeX Warning: Citation `novikov2021tensor' on page 3 undefined on input line 81.


LaTeX Warning: Citation `wall2022tensor' on page 3 undefined on input line 82.


LaTeX Warning: Citation `rieser2023tensor' on page 3 undefined on input line 82.


LaTeX Warning: Citation `stoudenmire2016supervised' on page 3 undefined on input line 84.

LaTeX Font Info:    Font shape `OT1/minntx/b/n' will be
(Font)              scaled to size 10.0pt on input line 84.
LaTeX Font Info:    Font shape `OT1/minntx/b/n' will be
(Font)              scaled to size 7.3pt on input line 84.
LaTeX Font Info:    Font shape `OT1/minntx/b/n' will be
(Font)              scaled to size 5.5pt on input line 84.


[3





]
./author001.tex:88: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.88 \end{align}
                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:88: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.88 \end{align}
                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:89: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.89 ...o higher-dimensional space and $\tensor{W}
                                                   \in \mathbb{R}^{d_1 \time...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Warning: Citation `stoudenmire2016supervised' on page 4 undefined on input line 89.

LaTeX Font Info:    Font shape `OML/ntxmi/b/it' will be
(Font)              scaled to size 10.0pt on input line 92.
LaTeX Font Info:    Font shape `OML/ntxmi/b/it' will be
(Font)              scaled to size 7.3pt on input line 92.
LaTeX Font Info:    Font shape `OML/ntxmi/b/it' will be
(Font)              scaled to size 5.5pt on input line 92.
LaTeX Font Info:    Font shape `LMS/ntxsy/b/n' will be
(Font)              scaled to size 10.0pt on input line 92.
LaTeX Font Info:    Font shape `LMS/ntxsy/b/n' will be
(Font)              scaled to size 7.3pt on input line 92.
LaTeX Font Info:    Font shape `LMS/ntxsy/b/n' will be
(Font)              scaled to size 5.5pt on input line 92.
LaTeX Font Info:    Font shape `LMX/ntxexx/b/n' will be
(Font)              scaled to size 10.0pt on input line 92.
LaTeX Font Info:    Font shape `LMX/ntxexx/b/n' will be
(Font)              scaled to size 7.3pt on input line 92.
LaTeX Font Info:    Font shape `LMX/ntxexx/b/n' will be
(Font)              scaled to size 5.5pt on input line 92.
LaTeX Font Info:    Font shape `U/ntxmia/b/it' will be
(Font)              scaled to size 10.0pt on input line 92.
LaTeX Font Info:    Font shape `U/ntxmia/b/it' will be
(Font)              scaled to size 7.3pt on input line 92.
LaTeX Font Info:    Font shape `U/ntxmia/b/it' will be
(Font)              scaled to size 5.5pt on input line 92.
LaTeX Font Info:    Font shape `U/ntxsym/b/n' will be
(Font)              scaled to size 10.0pt on input line 92.
LaTeX Font Info:    Font shape `U/ntxsym/b/n' will be
(Font)              scaled to size 7.3pt on input line 92.
LaTeX Font Info:    Font shape `U/ntxsym/b/n' will be
(Font)              scaled to size 5.5pt on input line 92.
LaTeX Font Info:    Font shape `U/ntxsyc/b/n' will be
(Font)              scaled to size 10.0pt on input line 92.
LaTeX Font Info:    Font shape `U/ntxsyc/b/n' will be
(Font)              scaled to size 7.3pt on input line 92.
LaTeX Font Info:    Font shape `U/ntxsyc/b/n' will be
(Font)              scaled to size 5.5pt on input line 92.
LaTeX Font Info:    Font shape `U/ntxexa/b/n' will be
(Font)              scaled to size 10.0pt on input line 92.
LaTeX Font Info:    Font shape `U/ntxexa/b/n' will be
(Font)              scaled to size 7.3pt on input line 92.
LaTeX Font Info:    Font shape `U/ntxexa/b/n' will be
(Font)              scaled to size 5.5pt on input line 92.
./author001.tex:94: Undefined control sequence.
l.94 Although $\mathbf \Phi(\bm
                                x)$ is a large tensor with $2^M$ elements, i...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:94: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.94 ..., the large coefficient tensor $\tensor{W}
                                                  $ can be expressed as Tens...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Warning: Citation `oseledets2011tensor' on page 4 undefined on input line 94.

./author001.tex:97: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.97 \end{align}
                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:97: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.97 \end{align}
                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:97: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.97 \end{align}
                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:97: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.97 \end{align}
                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:97: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.97 \end{align}
                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:97: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.97 \end{align}
                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:97: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.97 \end{align}
                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:97: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.97 \end{align}
                
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:98: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.98 Each $\tensor{A}
                     _j$ is a second or third order tensor called a core ten...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:98: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.98 ...essive power. In the TN model, $\tensor{W}
                                                  $ does not need to be expl...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:98: Undefined control sequence.
l.98 ...ined by contraction with $\mathbf \Phi(\bm
                                                   x)$ once the core tensors...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Warning: Reference `fig:TN' on page 4 undefined on input line 98.

<fig001/TNmodel.pdf, id=61, 963.6pt x 284.62335pt>
File: fig001/TNmodel.pdf Graphic file (type pdf)
<use fig001/TNmodel.pdf>
Package pdftex.def Info: fig001/TNmodel.pdf  used on input line 102.
(pdftex.def)             Requested size: 319.58357pt x 94.39503pt.
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 8.5pt on input line 103.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 7.3pt on input line 110.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 5.5pt on input line 110.
./author001.tex:110: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.110 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:110: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.110 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:112: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.112 Let $\theta=\{\tensor{A}
                              _1, ..., \tensor{A}_M, v\}$ denote the set of ...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:112: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.112 Let $\theta=\{\tensor{A}_1, ..., \tensor{A}
                                                 _M, v\}$ denote the set of ...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Warning: Reference `eq:l2_loss' on page 4 undefined on input line 112.



[4 <./fig001/TNmodel.pdf>]
./author001.tex:120: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.120 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:120: Undefined control sequence.
<argument>  (\tensor {A}_m,v) \leftarrow \argmin 
                                                 _{\tensor {A}_m,v} f(\tenso...
l.120 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:120: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.120 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:120: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.120 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:120: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.120 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:120: Undefined control sequence.
<argument>  (\tensor {A}_m,v) \leftarrow \argmin 
                                                 _{\tensor {A}_m,v} f(\tenso...
l.120 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:120: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.120 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:120: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.120 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Warning: Reference `eq:Am' on page 5 undefined on input line 121.


LaTeX Warning: Reference `fig:opt_l2' on page 5 undefined on input line 122.

LaTeX Font Info:    Font shape `OT1/minntx/b/n' will be
(Font)              scaled to size 8.5pt on input line 124.
LaTeX Font Info:    Font shape `OT1/minntx/b/n' will be
(Font)              scaled to size 6.20496pt on input line 124.
LaTeX Font Info:    Font shape `OT1/minntx/b/n' will be
(Font)              scaled to size 4.67502pt on input line 124.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 6.20496pt on input line 124.

LaTeX Warning: Citation `sun2016majorization' on page 5 undefined on input line 136.


LaTeX Warning: Citation `polson2013bayesian' on page 5 undefined on input line 137.


LaTeX Warning: Citation `scott2013expectation' on page 5 undefined on input line 137.

Package epstopdf Info: Source file: <fig001/optimization_behavior_l2.eps>
(epstopdf)                    date: 2025-05-30 05:48:32
(epstopdf)                    size: 197647 bytes
(epstopdf)             Output file: <fig001/optimization_behavior_l2-eps-converted-to.pdf>
(epstopdf)                    date: 2025-05-30 05:48:32
(epstopdf)                    size: 58741 bytes
(epstopdf)             Command: <repstopdf --outfile=fig001/optimization_behavior_l2-eps-converted-to.pdf fig001/optimization_behavior_l2.eps>
(epstopdf)             \includegraphics on input line 144.
Package epstopdf Info: Output file is already uptodate.
<fig001/optimization_behavior_l2-eps-converted-to.pdf, id=117, 348.30125pt x 211.79124pt>
File: fig001/optimization_behavior_l2-eps-converted-to.pdf Graphic file (type pdf)
<use fig001/optimization_behavior_l2-eps-converted-to.pdf>
Package pdftex.def Info: fig001/optimization_behavior_l2-eps-converted-to.pdf  used on input line 144.
(pdftex.def)             Requested size: 266.31879pt x 161.94867pt.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 11.0pt on input line 149.
LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 11.0pt on input line 149.

LaTeX Warning: Citation `polson2013bayesian' on page 5 undefined on input line 150.



[5]

LaTeX Warning: Reference `eq:PG_logistic_to_gauss' on page 6 undefined on input line 168.



[6 <./fig001/optimization_behavior_l2-eps-converted-to.pdf>]

LaTeX Warning: Reference `eq:logistic_loss' on page 7 undefined on input line 175.


LaTeX Warning: Reference `eq:PG_logistic_to_gauss' on page 7 undefined on input line 175.


LaTeX Warning: Reference `eq:PG_theorem' on page 7 undefined on input line 180.

Package epstopdf Info: Source file: <fig001/PG_based_majorization_of_logistic_loss.eps>
(epstopdf)                    date: 2025-05-30 05:48:32
(epstopdf)                    size: 333920 bytes
(epstopdf)             Output file: <fig001/PG_based_majorization_of_logistic_loss-eps-converted-to.pdf>
(epstopdf)                    date: 2025-05-30 05:48:32
(epstopdf)                    size: 130907 bytes
(epstopdf)             Command: <repstopdf --outfile=fig001/PG_based_majorization_of_logistic_loss-eps-converted-to.pdf fig001/PG_based_majorization_of_logistic_loss.eps>
(epstopdf)             \includegraphics on input line 192.
Package epstopdf Info: Output file is already uptodate.
<fig001/PG_based_majorization_of_logistic_loss-eps-converted-to.pdf, id=130, 393.47pt x 282.05376pt>
File: fig001/PG_based_majorization_of_logistic_loss-eps-converted-to.pdf Graphic file (type pdf)
<use fig001/PG_based_majorization_of_logistic_loss-eps-converted-to.pdf>
Package pdftex.def Info: fig001/PG_based_majorization_of_logistic_loss-eps-converted-to.pdf  used on input line 192.
(pdftex.def)             Requested size: 233.02704pt x 167.03862pt.

LaTeX Warning: Reference `fig:upper_bound' on page 7 undefined on input line 201.

./author001.tex:202: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.202 ...f x_n | \theta) $, $\theta = \{\tensor{A}
                                                  _1, ..., \tensor{A}_M, v\}...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:202: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.202 ...$\theta = \{\tensor{A}_1, ..., \tensor{A}
                                                  _M, v\}$, the overall auxi...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.



[7]
./author001.tex:219: Undefined control sequence.
<argument>  \theta ^{(t+1)} = \argmin 
                                      _\theta g(\theta | \theta ^{(t)}) 
l.219 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:219: Undefined control sequence.
<argument>  \theta ^{(t+1)} = \argmin 
                                      _\theta g(\theta | \theta ^{(t)}) 
l.219 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Warning: Reference `alg:TNEM' on page 8 undefined on input line 225.


LaTeX Warning: Reference `fig:fsweep' on page 8 undefined on input line 227.



[8 <./fig001/PG_based_majorization_of_logistic_loss-eps-converted-to.pdf>]
./author001.tex:232: Undefined control sequence.
l.232   \STATE
               Initialize $\theta = \{\tensor{A}_1, \tensor{A}_2, ..., \tens...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:232: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.232   \STATE Initialize $\theta = \{\tensor{A}
                                                _1, \tensor{A}_2, ..., \tens...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:232: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.232 ...lize $\theta = \{\tensor{A}_1, \tensor{A}
                                                  _2, ..., \tensor{A}_M, v\}$
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:232: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.232 ...ensor{A}_1, \tensor{A}_2, ..., \tensor{A}
                                                  _M, v\}$
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:233: Undefined control sequence.
l.233   \REPEAT
               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:234: Undefined control sequence.
l.234   \STATE
               (Forward Sweep)
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:235: Undefined control sequence.
l.235   \FOR
            {$m = 1, 2, ..., M-1$}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:236: Undefined control sequence.
l.236     \STATE
                 Calculate $\omega_n$ from the current $\theta$;
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:237: Undefined control sequence.
l.237     \STATE
                 $(\tensor{A}_m, v) \leftarrow \argmin_{(\tensor{A}_m,v)} g(...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:237: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.237     \STATE $(\tensor{A}
                             _m, v) \leftarrow \argmin_{(\tensor{A}_m,v)} g(...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:237: Undefined control sequence.
l.237 ...ATE $(\tensor{A}_m, v) \leftarrow \argmin
                                                  _{(\tensor{A}_m,v)} g(\ten...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:237: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.237 ...{A}_m, v) \leftarrow \argmin_{(\tensor{A}
                                                  _m,v)} g(\tensor{A}_m, v |...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:237: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.237 ...w \argmin_{(\tensor{A}_m,v)} g(\tensor{A}
                                                  _m, v | \theta)$;
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:238: Undefined control sequence.
l.238     \STATE
                 Orthogonalize $\tensor{A}_m$ by QR decomposition and integr...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:238: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.238     \STATE Orthogonalize $\tensor{A}
                                          _m$ by QR decomposition and integr...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:238: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.238 ... upper triangular matrix into $\tensor{A}
                                                  _{m+1}$;
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:239: Undefined control sequence.
l.239     \STATE
                 Overwrite $\theta = \{\tensor{A}_1, \tensor{A}_2, ..., \ten...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:239: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.239     \STATE Overwrite $\theta = \{\tensor{A}
                                                 _1, \tensor{A}_2, ..., \ten...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:239: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.239 ...rite $\theta = \{\tensor{A}_1, \tensor{A}
                                                  _2, ..., \tensor{A}_M, v\}...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:239: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.239 ...ensor{A}_1, \tensor{A}_2, ..., \tensor{A}
                                                  _M, v\}$ with the latest p...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:240: Undefined control sequence.
l.240   \ENDFOR
               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:241: Undefined control sequence.
l.241   \STATE
               (Backward Sweep)
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:242: Undefined control sequence.
l.242   \FOR
            {$m = M, M-1, ..., 2$}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:243: Undefined control sequence.
l.243     \STATE
                 Calculate $\omega_n$ from the current $\theta$;
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:244: Undefined control sequence.
l.244     \STATE
                 $(\tensor{A}_m, v) \leftarrow \argmin_{(\tensor{A}_m,v)} g(...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:244: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.244     \STATE $(\tensor{A}
                             _m, v) \leftarrow \argmin_{(\tensor{A}_m,v)} g(...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:244: Undefined control sequence.
l.244 ...ATE $(\tensor{A}_m, v) \leftarrow \argmin
                                                  _{(\tensor{A}_m,v)} g(\ten...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:244: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.244 ...{A}_m, v) \leftarrow \argmin_{(\tensor{A}
                                                  _m,v)} g(\tensor{A}_m, v |...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:244: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.244 ...w \argmin_{(\tensor{A}_m,v)} g(\tensor{A}
                                                  _m, v | \theta)$;
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:245: Undefined control sequence.
l.245     \STATE
                 Orthogonalize $\tensor{A}_m$ by QR decomposition and integr...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:245: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.245     \STATE Orthogonalize $\tensor{A}
                                          _m$ by QR decomposition and integr...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:245: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.245 ... upper triangular matrix into $\tensor{A}
                                                  _{m-1}$;
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:246: Undefined control sequence.
l.246     \STATE
                 Overwrite $\theta = \{\tensor{A}_1, \tensor{A}_2, ..., \ten...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:246: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.246     \STATE Overwrite $\theta = \{\tensor{A}
                                                 _1, \tensor{A}_2, ..., \ten...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:246: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.246 ...rite $\theta = \{\tensor{A}_1, \tensor{A}
                                                  _2, ..., \tensor{A}_M, v\}...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:246: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.246 ...ensor{A}_1, \tensor{A}_2, ..., \tensor{A}
                                                  _M, v\}$ with the latest p...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:247: Undefined control sequence.
l.247   \ENDFOR
               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:248: Undefined control sequence.
l.248   \UNTIL
              {Convergence}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


./author001.tex:249: LaTeX Error: Something's wrong--perhaps a missing \item.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.249 \end{algorithmic}
                       
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

<fig001/45.pdf, id=139, 569.0058pt x 227.6505pt>
File: fig001/45.pdf Graphic file (type pdf)
<use fig001/45.pdf>
Package pdftex.def Info: fig001/45.pdf  used on input line 255.
(pdftex.def)             Requested size: 246.34578pt x 98.55821pt.

LaTeX Warning: Reference `fig:TN_sub' on page 9 undefined on input line 267.

./author001.tex:270: Undefined control sequence.
<argument> ...angle {\mathbf l_n^{(m)}\otimes \bm 
                                                  \phi _n^{ (m)}\otimes {\ma...
l.270 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:270: Undefined control sequence.
<argument> ...angle {\mathbf l_n^{(m)}\otimes \bm 
                                                  \phi _n^{ (m)}\otimes {\ma...
l.270 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:271: Undefined control sequence.
<recently read> \bm 
                    
l.271 ...etting the optimization parameter as $\bm
                                                  \beta = [\text{vec}(\tenso...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:271: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.271 ...ter as $\bm\beta = [\text{vec}(\tensor{A}
                                                  _m)^\top, v]^\top $, the s...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:271: Undefined control sequence.
<recently read> \bm 
                    
l.271 ...m)^\top, v]^\top $, the solution for $\bm
                                                  \beta$ of the auxiliary fu...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Warning: Reference `eq:sum_of_WLS' on page 9 undefined on input line 271.

./author001.tex:274: Undefined control sequence.
<recently read> \bm 
                    
l.274 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:274: Undefined control sequence.
<argument> \bm 
               \beta 
l.274 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:274: Undefined control sequence.
<argument> ...thbf {Z})^{-1} \mathbf {Z}^\top \bm 
                                                  \kappa , 
l.274 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:274: Undefined control sequence.
<recently read> \bm 
                    
l.274 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:274: Undefined control sequence.
<argument> \bm 
               \beta 
l.274 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:274: Undefined control sequence.
<argument> ...thbf {Z})^{-1} \mathbf {Z}^\top \bm 
                                                  \kappa , 
l.274 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:284: Undefined control sequence.
<argument>  & \bm 
                  \kappa = \left [ \kappa _1, \kappa _2, ..., \kappa _N \rig...
l.284 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:284: Undefined control sequence.
<argument> ...trix} (\mathbf l_1^{(m)}\otimes \bm 
                                                  \phi _1^{ (m)}\otimes {\ma...
l.284 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:284: Undefined control sequence.
<argument> ... 1 \\ (\mathbf l_2^{(m)}\otimes \bm 
                                                  \phi _2^{ (m)}\otimes {\ma...
l.284 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:284: Undefined control sequence.
<argument> ...ts \\ (\mathbf l_N^{(m)}\otimes \bm 
                                                  \phi _N^{ (m)}\otimes {\ma...
l.284 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:284: Undefined control sequence.
<argument>  & \bm 
                  \kappa = \left [ \kappa _1, \kappa _2, ..., \kappa _N \rig...
l.284 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:284: Undefined control sequence.
<argument> ...trix} (\mathbf l_1^{(m)}\otimes \bm 
                                                  \phi _1^{ (m)}\otimes {\ma...
l.284 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:284: Undefined control sequence.
<argument> ... 1 \\ (\mathbf l_2^{(m)}\otimes \bm 
                                                  \phi _2^{ (m)}\otimes {\ma...
l.284 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:284: Undefined control sequence.
<argument> ...ts \\ (\mathbf l_N^{(m)}\otimes \bm 
                                                  \phi _N^{ (m)}\otimes {\ma...
l.284 \end{align}
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.



[9 <./fig001/45.pdf>]
./author001.tex:285: Undefined control sequence.
<recently read> \bm 
                    
l.285 ...he Tikonov regularization $\hat{\bm\beta}
                                                   = (\mathbf{Z}^\top \mathb...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:285: Undefined control sequence.
<argument> \bm 
               \beta 
l.285 ...he Tikonov regularization $\hat{\bm\beta}
                                                   = (\mathbf{Z}^\top \mathb...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author001.tex:285: Undefined control sequence.
l.285 ...ilon \mathbf I )^{-1} \mathbf{Z}^\top \bm
                                                  \kappa$ with positive hype...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

<fig001/4_4.pdf, id=155, 317.185pt x 301.125pt>
File: fig001/4_4.pdf Graphic file (type pdf)
<use fig001/4_4.pdf>
Package pdftex.def Info: fig001/4_4.pdf  used on input line 289.
(pdftex.def)             Requested size: 166.44861pt x 158.02406pt.
./author001.tex:298: Undefined control sequence.
<recently read> \bm 
                    
l.298 ...lues to [0,1]. 49-dimensional vector $\bm
                                                   x \in [0,1]^{49}$ was use...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Warning: Reference `fig:opt_behav' on page 10 undefined on input line 300.

./author001.tex:301: Undefined control sequence.
\tensor #1->\bm 
                {\mathcal {#1}}
l.301 ...of times a partial update of $(\tensor{A}
                                                  _m,v)$ is performed (i.e.,...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Warning: Reference `fig:opt_behav' on page 10 undefined on input line 303.



[10 <./fig001/4_4.pdf>]
Package epstopdf Info: Source file: <fig001/rank_loss.eps>
(epstopdf)                    date: 2025-05-30 05:48:32
(epstopdf)                    size: 74481 bytes
(epstopdf)             Output file: <fig001/rank_loss-eps-converted-to.pdf>
(epstopdf)                    date: 2025-05-30 05:48:32
(epstopdf)                    size: 27088 bytes
(epstopdf)             Command: <repstopdf --outfile=fig001/rank_loss-eps-converted-to.pdf fig001/rank_loss.eps>
(epstopdf)             \includegraphics on input line 308.
Package epstopdf Info: Output file is already uptodate.
<fig001/rank_loss-eps-converted-to.pdf, id=182, 326.21875pt x 219.82124pt>
File: fig001/rank_loss-eps-converted-to.pdf Graphic file (type pdf)
<use fig001/rank_loss-eps-converted-to.pdf>
Package pdftex.def Info: fig001/rank_loss-eps-converted-to.pdf  used on input line 308.
(pdftex.def)             Requested size: 233.02704pt x 157.0301pt.


LaTeX Warning: Reference `fig:acc_behav' on page 11 undefined on input line 313.


LaTeX Warning: Reference `tab:comp' on page 11 undefined on input line 316.

Package epstopdf Info: Source file: <fig001/accuracyrank.eps>
(epstopdf)                    date: 2025-05-30 05:48:32
(epstopdf)                    size: 122950 bytes
(epstopdf)             Output file: <fig001/accuracyrank-eps-converted-to.pdf>
(epstopdf)                    date: 2025-05-30 05:48:32
(epstopdf)                    size: 54463 bytes
(epstopdf)             Command: <repstopdf --outfile=fig001/accuracyrank-eps-converted-to.pdf fig001/accuracyrank.eps>
(epstopdf)             \includegraphics on input line 320.
Package epstopdf Info: Output file is already uptodate.
<fig001/accuracyrank-eps-converted-to.pdf, id=183, 388.45125pt x 218.8175pt>
File: fig001/accuracyrank-eps-converted-to.pdf Graphic file (type pdf)
<use fig001/accuracyrank-eps-converted-to.pdf>
Package pdftex.def Info: fig001/accuracyrank-eps-converted-to.pdf  used on input line 320.
(pdftex.def)             Requested size: 233.02704pt x 131.26813pt.


[11 <./fig001/rank_loss-eps-converted-to.pdf>] (./references001.tex
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/it' will be
(Font)              scaled to size 8.5pt on input line 12.
LaTeX Info: Symbol \textasciidieresis not provided by
            font family ntxtlf in TS1 encoding.
            Default family used instead on input line 24.


LaTeX Font Warning: Font shape `OT1/cmr/m/n' in size <8.5> not available
(Font)              size <8> substituted on input line 24.


LaTeX Font Warning: Font shape `TS1/cmr/m/n' in size <8.5> not available
(Font)              size <8> substituted on input line 24.



[12{/usr/local/texlive/2025/texmf-dist/fonts/enc/dvips/cm-super/cm-super-ts1.enc} <./fig001/accuracyrank-eps-converted-to.pdf>]))

[13]
\openout2 = `author002.aux'.

 (./author002.tex

[14





]
Underfull \hbox (badness 10000) in paragraph at lines 71--71
\OT1/minntx/m/n/8.5 emails: cai[]<EMAIL>, <EMAIL>, <EMAIL>,
 []

Title too long for running head. Please supply
a shorter form with \titlerunning prior to \maketitle

LaTeX Warning: Citation `1927The' on page 15 undefined on input line 81.


LaTeX Warning: Citation `2017Tensor' on page 15 undefined on input line 81.


LaTeX Warning: Citation `2015Tensor' on page 15 undefined on input line 81.


LaTeX Warning: Citation `2016Linked' on page 15 undefined on input line 81.


LaTeX Warning: Citation `2011application' on page 15 undefined on input line 81.


LaTeX Warning: Citation `2014lowrank' on page 15 undefined on input line 81.


LaTeX Warning: Citation `1927The' on page 15 undefined on input line 81.


LaTeX Warning: Citation `1966some' on page 15 undefined on input line 81.


LaTeX Warning: Citation `1999para' on page 15 undefined on input line 81.


LaTeX Warning: Citation `2016Linked' on page 15 undefined on input line 81.


LaTeX Warning: Citation `2013structure' on page 15 undefined on input line 81.


LaTeX Warning: Citation `2019unravel' on page 15 undefined on input line 81.


LaTeX Warning: Citation `2020group' on page 15 undefined on input line 81.


LaTeX Warning: Citation `2022exploring' on page 15 undefined on input line 81.


LaTeX Warning: Citation `2017tensor' on page 15 undefined on input line 81.


LaTeX Warning: Citation `2004concurrent' on page 15 undefined on input line 81.


LaTeX Warning: Citation `2017tensorbased' on page 15 undefined on input line 81.


LaTeX Warning: Citation `2020extraction' on page 15 undefined on input line 81.



[15]

LaTeX Warning: Citation `2017federated' on page 16 undefined on input line 83.


LaTeX Warning: Citation `2024FCNCP' on page 16 undefined on input line 83.


LaTeX Warning: Citation `2021common' on page 16 undefined on input line 85.


LaTeX Warning: Citation `2004validating' on page 16 undefined on input line 85.


LaTeX Warning: Citation `2013Neuro' on page 16 undefined on input line 85.


LaTeX Warning: Citation `2010Reliable' on page 16 undefined on input line 85.


LaTeX Warning: Citation `2019Multiway' on page 16 undefined on input line 85.


LaTeX Warning: Citation `2017Tree' on page 16 undefined on input line 85.


LaTeX Warning: Citation `2019Co' on page 16 undefined on input line 85.


LaTeX Warning: Citation `2022Discorvering' on page 16 undefined on input line 85.



[16]

LaTeX Warning: Citation `2024FCNCP' on page 17 undefined on input line 87.



[17]
<fig002/fig1.pdf, id=213, 962.59625pt x 399.4925pt>
File: fig002/fig1.pdf Graphic file (type pdf)
<use fig002/fig1.pdf>
Package pdftex.def Info: fig002/fig1.pdf  used on input line 108.
(pdftex.def)             Requested size: 341.43306pt x 141.69586pt.

Overfull \hbox (8.53583pt too wide) in paragraph at lines 108--109
 [][] 
 []


LaTeX Warning: Citation `2024FCNCP' on page 18 undefined on input line 115.


LaTeX Warning: Reference `fig1' on page 18 undefined on input line 115.



[18 <./fig002/fig1.pdf>]

LaTeX Warning: Citation `2022Discorvering' on page 19 undefined on input line 129.



[19]

LaTeX Warning: Reference `fig2' on page 20 undefined on input line 145.

<fig002/fig2.pdf, id=222, 317.185pt x 541.02126pt>
File: fig002/fig2.pdf Graphic file (type pdf)
<use fig002/fig2.pdf>
Package pdftex.def Info: fig002/fig2.pdf  used on input line 149.
(pdftex.def)             Requested size: 176.407pt x 300.906pt.
LaTeX Font Info:    Trying to load font information for TS1+ntxtlf on input line 168.
(/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/ts1ntxtlf.fd
File: ts1ntxtlf.fd 2015/01/18 v1.0 fd file for TS1/ntxtlf
)
LaTeX Font Info:    Font shape `TS1/ntxtlf/m/n' will be
(Font)              scaled to size 10.0pt on input line 168.


[20]

LaTeX Warning: Reference `fig3a' on page 21 undefined on input line 177.


LaTeX Warning: Reference `fig3b' on page 21 undefined on input line 177.

./author002.tex:187: Undefined control sequence.
l.187 \subfigure
                []{
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

<fig002/fig3a.pdf, id=226, 437.635pt x 403.5075pt>
File: fig002/fig3a.pdf Graphic file (type pdf)
<use fig002/fig3a.pdf>
Package pdftex.def Info: fig002/fig3a.pdf  used on input line 189.
(pdftex.def)             Requested size: 142.26378pt x 131.16916pt.
./author002.tex:191: Undefined control sequence.
l.191 \subfigure
                []{
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

<fig002/fig3b.pdf, id=227, 437.635pt x 403.5075pt>
File: fig002/fig3b.pdf Graphic file (type pdf)
<use fig002/fig3b.pdf>
Package pdftex.def Info: fig002/fig3b.pdf  used on input line 193.
(pdftex.def)             Requested size: 142.26378pt x 131.16916pt.
./author002.tex:194: Undefined control sequence.
l.194 \subfigure
                []{
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

<fig002/fig3c.pdf, id=228, 437.635pt x 403.5075pt>
File: fig002/fig3c.pdf Graphic file (type pdf)
<use fig002/fig3c.pdf>
Package pdftex.def Info: fig002/fig3c.pdf  used on input line 196.
(pdftex.def)             Requested size: 142.26378pt x 131.16916pt.
./author002.tex:198: Undefined control sequence.
l.198 \subfigure
                []{
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

<fig002/fig3d.pdf, id=229, 437.635pt x 403.5075pt>
File: fig002/fig3d.pdf Graphic file (type pdf)
<use fig002/fig3d.pdf>
Package pdftex.def Info: fig002/fig3d.pdf  used on input line 200.
(pdftex.def)             Requested size: 142.26378pt x 131.16916pt.


[21{/usr/local/texlive/2025/texmf-dist/fonts/enc/dvips/tex-gyre/q-ts1.enc} <./fig002/fig2.pdf>]
./author002.tex:209: Undefined control sequence.
l.209 \subfigtopskip
                    =2pt
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author002.tex:210: Undefined control sequence.
l.210 \subfigbottomskip
                       =7pt
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author002.tex:211: Undefined control sequence.
l.211 \subfigcapskip
                    =-5pt
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author002.tex:212: Undefined control sequence.
l.212 \subfigure
                []{
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

<fig002/fig4a.pdf, id=236, 433.62pt x 375.4025pt>
File: fig002/fig4a.pdf Graphic file (type pdf)
<use fig002/fig4a.pdf>
Package pdftex.def Info: fig002/fig4a.pdf  used on input line 214.
(pdftex.def)             Requested size: 142.26378pt x 123.16145pt.
./author002.tex:217: Undefined control sequence.
l.217 \subfigure
                []{
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

<fig002/fig4b.pdf, id=237, 433.62pt x 375.4025pt>
File: fig002/fig4b.pdf Graphic file (type pdf)
<use fig002/fig4b.pdf>
Package pdftex.def Info: fig002/fig4b.pdf  used on input line 219.
(pdftex.def)             Requested size: 142.26378pt x 123.16145pt.

LaTeX Warning: Reference `fig4' on page 22 undefined on input line 225.


LaTeX Warning: Citation `2007atool' on page 22 undefined on input line 230.

./author002.tex:230: Undefined control sequence.
l.230 ...tool}, which can be downloaded from \href
                                                  {www.erpwavelab.org}{www.e...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Warning: Citation `2006decomposing' on page 22 undefined on input line 230.


LaTeX Warning: Citation `2004EEGLAB' on page 22 undefined on input line 230.


LaTeX Warning: Citation `2006mechanisms' on page 22 undefined on input line 230.


LaTeX Warning: Citation `2004EEGLAB' on page 22 undefined on input line 230.


LaTeX Warning: Citation `2014analyzing' on page 22 undefined on input line 230.



[22 <./fig002/fig3a.pdf> <./fig002/fig3b.pdf> <./fig002/fig3c.pdf> <./fig002/fig3d.pdf>]

[23 <./fig002/fig4a.pdf> <./fig002/fig4b.pdf>]

LaTeX Warning: Citation `2024FCNCP' on page 24 undefined on input line 233.

LaTeX Font Info:    Trying to load font information for OMS+ntxtlf on input line 233.
LaTeX Font Info:    No file OMSntxtlf.fd. on input line 233.

LaTeX Font Warning: Font shape `OMS/ntxtlf/m/n' undefined
(Font)              using `OMS/cmsy/m/n' instead
(Font)              for symbol `textbraceleft' on input line 233.


LaTeX Warning: Reference `fig7' on page 24 undefined on input line 236.


LaTeX Warning: Reference `fig5' on page 24 undefined on input line 236.


LaTeX Warning: Reference `fig6' on page 24 undefined on input line 236.


LaTeX Warning: Reference `fig5' on page 24 undefined on input line 236.


LaTeX Warning: Reference `fig6' on page 24 undefined on input line 236.


LaTeX Warning: Citation `2018extracting' on page 24 undefined on input line 236.

./author002.tex:243: Undefined control sequence.
l.243     \subfigure
                    []{\includegraphics[width=10cm]{fig002/fig5a.pdf}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

<fig002/fig5a.pdf, id=257, 1240.635pt x 537.00626pt>
File: fig002/fig5a.pdf Graphic file (type pdf)
<use fig002/fig5a.pdf>
Package pdftex.def Info: fig002/fig5a.pdf  used on input line 243.
(pdftex.def)             Requested size: 284.52756pt x 123.1565pt.
./author002.tex:245: Undefined control sequence.
l.245     \subfigure
                    []{\includegraphics[width=10cm]{fig002/fig5b.pdf}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

<fig002/fig5b.pdf, id=258, 1240.635pt x 537.00626pt>
File: fig002/fig5b.pdf Graphic file (type pdf)
<use fig002/fig5b.pdf>
Package pdftex.def Info: fig002/fig5b.pdf  used on input line 245.
(pdftex.def)             Requested size: 284.52756pt x 123.1565pt.
./author002.tex:247: Undefined control sequence.
l.247     \subfigure
                    []{\includegraphics[width=10cm]{fig002/fig5c.pdf}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

<fig002/fig5c.pdf, id=259, 1240.635pt x 537.00626pt>
File: fig002/fig5c.pdf Graphic file (type pdf)
<use fig002/fig5c.pdf>
Package pdftex.def Info: fig002/fig5c.pdf  used on input line 247.
(pdftex.def)             Requested size: 284.52756pt x 123.1565pt.
./author002.tex:255: Undefined control sequence.
l.255     \subfigtopskip
                        =2pt
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author002.tex:256: Undefined control sequence.
l.256     \subfigbottomskip
                           =2pt
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author002.tex:257: Undefined control sequence.
l.257     \subfigcapskip
                        =-5pt
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author002.tex:258: Undefined control sequence.
l.258     \subfigure
                    []{\includegraphics[width=12cm]{fig002/fig6a.pdf}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

<fig002/fig6a.pdf, id=260, 1240.635pt x 537.00626pt>
File: fig002/fig6a.pdf Graphic file (type pdf)
<use fig002/fig6a.pdf>
Package pdftex.def Info: fig002/fig6a.pdf  used on input line 258.
(pdftex.def)             Requested size: 341.43306pt x 147.7878pt.
./author002.tex:260: Undefined control sequence.
l.260     \subfigure
                    []{\includegraphics[width=12cm]{fig002/fig6b.pdf}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

<fig002/fig6b.pdf, id=261, 1240.635pt x 537.00626pt>
File: fig002/fig6b.pdf Graphic file (type pdf)
<use fig002/fig6b.pdf>
Package pdftex.def Info: fig002/fig6b.pdf  used on input line 260.
(pdftex.def)             Requested size: 341.43306pt x 147.7878pt.

Overfull \hbox (14.19681pt too wide) in paragraph at lines 255--262
 \OT1/minntx/m/n/8.5 [][] 
 []


Overfull \hbox (15.47176pt too wide) in paragraph at lines 255--262
 [] \OT1/minntx/m/n/8.5 [][] 
 []

./author002.tex:270: Undefined control sequence.
l.270 \subfigtopskip
                    =2pt
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author002.tex:271: Undefined control sequence.
l.271 \subfigbottomskip
                       =7pt
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author002.tex:272: Undefined control sequence.
l.272 \subfigcapskip
                    =-5pt
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./author002.tex:274: Undefined control sequence.
l.274 \subfigure
                []{
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

<fig002/fig7a.pdf, id=262, 769.87625pt x 503.8825pt>
File: fig002/fig7a.pdf Graphic file (type pdf)
<use fig002/fig7a.pdf>
Package pdftex.def Info: fig002/fig7a.pdf  used on input line 276.
(pdftex.def)             Requested size: 165.02606pt x 108.0097pt.
./author002.tex:279: Undefined control sequence.
l.279 \subfigure
                []{
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

<fig002/fig7b.pdf, id=263, 769.87625pt x 503.8825pt>
File: fig002/fig7b.pdf Graphic file (type pdf)
<use fig002/fig7b.pdf>
Package pdftex.def Info: fig002/fig7b.pdf  used on input line 281.
(pdftex.def)             Requested size: 165.02606pt x 108.0097pt.


[24]

[25 <./fig002/fig5a.pdf> <./fig002/fig5b.pdf> <./fig002/fig5c.pdf>]

[26 <./fig002/fig6a.pdf> <./fig002/fig6b.pdf>] (./references002.tex

[27 <./fig002/fig7a.pdf> <./fig002/fig7b.pdf>]

[28]))

[29]
\openout2 = `part2.aux'.

 (./part2.tex

[30





]

[31]

[32])
/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/Advances in Artificial Intelligence_ Efficiency, Reliability, and Innovations in Machine Learning to Healthcare, and Blockchain/editor/editor.tex:96: I can't write on file `"editor/Chapter 2/3/sn-article.aux"'.
\@include ...mmediate \openout \@partaux "#1.aux" 
                                                  \immediate \write \@partau...
l.96 \include{editor/Chapter 2/3/sn-article}
                                            
(Press Enter to retry, or Control-D to exit; default file extension is `.tex')
Please type another output file name
/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/Advances in Artificial Intelligence_ Efficiency, Reliability, and Innovations in Machine Learning to Healthcare, and Blockchain/editor/editor.tex:96: Emergency stop.
\@include ...mmediate \openout \@partaux "#1.aux" 
                                                  \immediate \write \@partau...
l.96 \include{editor/Chapter 2/3/sn-article}
                                            
*** (job aborted, file error in nonstop mode)

 
Here is how much of TeX's memory you used:
 12219 strings out of 473190
 184390 string characters out of 5715800
 596180 words of memory out of 5000000
 35142 multiletter control sequences out of 15000+600000
 637241 words of font info for 203 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 80i,19n,93p,3629b,360s stack positions out of 10000i,1000n,20000p,200000b,200000s
/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/Advances in Artificial Intelligence_ Efficiency, Reliability, and Innovations in Machine Learning to Healthcare, and Blockchain/editor/editor.tex:96:  ==> Fatal error occurred, no output PDF file produced!
