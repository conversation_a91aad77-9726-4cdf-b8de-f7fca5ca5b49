% ---- Bibliography ----
%
% BibTeX users should specify bibliography style 'splncs04'.
% References will then be sorted and formatted in the correct style.
%
% \bibliographystyle{splncs04}
% \bibliography{mybibliography}
%
\begin{thebibliography}{8}
\bibitem{1927The} <PERSON>, F. L.: The expression of a tensor or a polyadic as a sum of products. Journal of Mathematics and Physics \textbf{6}(1), 164--189 (1927)
\bibitem{2017Tensor} <PERSON>, N. D., Lathauwer,  L. D., Fu, X., Huang <PERSON>, <PERSON><PERSON>, E. E., and Faloutsos, C.: Tensor decomposition for signal processing and machine learning. IEEE Transactions on Signal Processing \textbf{65}(13), 3551--3582 (2017)
\bibitem{2015Tensor} Cichocki, A., Mandic, D., Lathauwer, L. D., <PERSON>, <PERSON>, <PERSON>, <PERSON>, Caiafa, C., and Phan, H. A.: Tensor decompositions for signal processing applications: From two-way to multiway component analysis. IEEE signal processing magazine \textbf{32}(2), 145--163 (2015)
\bibitem{2016L<PERSON>ed} <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, T., <PERSON>, S., and <PERSON><PERSON>i, A.: Linked component analysis from matrices to high-order tensors: Applications to biomedical data. Proceedings of the IEEE \textbf{104}(2), 310--331 (2016)
\bibitem{2011application} M{\o}rup, M.: Applications of tensor (multiway array) factorizations and decompositions in data mining. Wiley Interdisciplinary Reviews: Data Mining and Knowledge Discovery \textbf{1}(1), 24--40 (2011)
\bibitem{2014lowrank} Cong, F. Y., Zhou, G., Astikainen, P., Zhao, Q., Wu, Q., and Nandi, A. K.: Low-rank approximation based non-negative multi-way array decomposition on event-related potentials. International Journal of Neural Systems \textbf{24}(8), 1440005(19 pages) (2014)
\bibitem{1966some} Tucker, L. R.: Some mathematical notes on three-mode factor analysis. Psychometrika \textbf{31}(3), 279--311 (1966)
\bibitem{1999para} Kiers, H. A., Ten Berge, J. M., and Bro, R.: PARAFAC2—Part I. A direct fitting algorithm for the PARAFAC2 model. Journal of Chemometrics: A Journal of the Chemometrics Society \textbf{13}(3), 275--294 (1999)
\bibitem{2013structure} Acar, E., Lawaetz, A. J., Rasmussen, M. A., and Bro, R.: Structure-revealing data fusion model with applications in metabolomics. In: EMBC, pp. 6023--6026. (2013).
\bibitem{2019unravel} Acar, E., Schenker, C., Levin-Schwarts, Y., Calhoun, V. D., and Adali, T.: Unraveling diagnostic biomarkers of schizophrenia through structure-revealing fusion of multi-modal neuroimaging data. Frontiers in neuroscience \textbf{13}, 416 (2019)
\bibitem{2020group} Wang, X., Liu, W., Toiviainen, P., Ristaniemi, T., and Cong, F.: Group analysis of ongoing EEG data based on fast double-coupled nonnegative tensor decomposition. Journal of neuroscience methods \textbf{330}, 108502 (2020)
\bibitem{2022exploring} Liu, W., Wang, X., H$\ddot{a}$m$\ddot{a}$l$\ddot{a}$inen, T.: Exploring oscillatory dysconnectivity networks in major depression during resting state using coupled tensor decomposition. IEEE Transactions on Biomedical Engineering \textbf{69}(8), 2691--2700 (2022)
\bibitem{2017tensor} Hunyadi, B., Dupont, P., Van Paesschen, W., and Van Huffel, S.:  Tensor decompositions and data fusion in epileptic electroencephalography and functional magnetic resonance imaging data. Wiley Interdisciplinary Reviews: Data Mining and Knowledge Discovery \textbf{7}(1), e1197 (2017)
\bibitem{2004concurrent} Martınez-Montes, E., Vald{'e}s-Sosa, P. A., Miwakeichi, F., Goldman, R. I., and Cohen, M. S.: Concurrent EEG/fMRI analysis by multiway partial least squares. NeuroImage \textbf{22}(3), 1023--1034 (2004)
\bibitem{2017tensorbased} Acar, E., Levin-Schwartz, Y., Calhoun, V. D., and Adali, T.: Tensor-based fusion of EEG and FMRI to understand neurological changes in schizophrenia. In 2017 IEEE International Symposium on Circuits and Systems (ISCAS), pp. 1--4 (2017).
\bibitem{2020extraction} Jonmohamadi, Y., Muthukumaraswamy, S., Chen, J., Roberts, J., Crawford, R., and Pandey, A.: Extraction of common task features in EEG-fMRI data using coupled tensor-tensor decomposition. Brain Topography \textbf{33}(5), 636--650 (2020)
\bibitem{2017federated} Kim, Y., Sun, J., Yu, H., and Jiang, X.: Federated tensor factorization for computational phenotyping. In Proceedings of the 23rd ACM SIGKDD International Conference on Knowledge Discovery and Data Mining, pp. 887--895 (2017)
\bibitem{2024FCNCP} Cai, Y. K., Liu, H., Wang, X. L., Li, H. J., Wang, Z. Y., Yang, C. S., and Cong, F. Y.: FCNCP: A Coupled Nonnegative CANDECOMP/PARAFAC Decomposition Based on Federated Learning. arXiv preprint (2024)
\bibitem{2021common} Roberts, M., Driggs, D., Thorpe, M., Gilbey, J., Yeung, M., Ursprung, S., and Schonlieb, C. B.: Common pitfalls and recommendations for using machine learning to detect and prognosticate for COVID-19 using chest radiographs and CT scans. Nature Machine Intelligence \textbf{3}(3), 199--217 (2021)
\bibitem{2004validating} Himberg, J., Hyvarinen, A., and Esposito, F.: Validating the independent components of neuroimaging time series via clustering and visualization. Neuroimage, \textbf{22}(3), 1214--1222 (2004)
\bibitem{2013Neuro} Wisner, K. M., Atluri, G., Lim, K. O., and MacDonald III, A. W.: eurometrics of intrinsic connectivity networks at rest using fMRI: retest reliability and cross-validation using a meta-level method. Neuroimage \textbf{76}, 236--251 (2013)
\bibitem{2010Reliable} Zuo, X. N., Kelly, C., Adelstein, J. S., klein, D. F., Castellanos, F. X., and Milham, M. P.: Reliable intrinsic connectivity networks: test–retest evaluation using ICA and dual regression approach.  Neuroimage, \textbf{49}(3), 2163--2177 (2010)
\bibitem{2019Multiway} Wang, M., and Zheng, Y.: Multiway clustering via tensor block models. In Advances in neural information processing systems (2019)
\bibitem{2017Tree} Rastogi, R., and Sharma, S.: Tree-based structural twin support tensor clustering with square loss function. In International Conference on Pattern Recognition and Machine Intelligence, pp. 28--34 (2017)
\bibitem{2019Co} Forero, P. A., Baxley, P. A., and Capella, M.: Co-clustering of high-order data via regularized Tucker decompositions. In ICASSP 2019-2019 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP), pp. 3442-3446, (2019)
\bibitem{2022Discorvering} Hu, G., Li, H., Zhao, W., Hao, Y., Bai, Z., Nickerson, L. D., and Cong, F.: Discovering hidden brain network responses to naturalistic stimuli via tensor component analysis of multi-subject fMRI data. NeuroImage, 119193 (2022)
\bibitem{2007atool} M{\o}rup, M., Hansen, L. K., and Arnfred, S. M.: ERPWAVELAB: a toolbox for multi-channel analysis of time–frequency transformed event related potentials. Journal of neuroscience methods \textbf{161}(2), 361--368 (2007)
\bibitem{2006decomposing} M{\o}rup, M., Hansen, L. K., Parnas, J., and Arnfred, S. M., Decomposing the time-frequency representation of EEG using non-negative matrix and multi-way factorization. university of denmark (2006)
\bibitem{2004EEGLAB} Delorme, A., and Makeig, S.: EEGLAB: an open source toolbox for analysis of single-trial EEG dynamics including independent component analysis. Journal of neuroscience methods \textbf{134}(1), 9--21 (2004)
\bibitem{2006mechanisms} David, O., Kilner, J. M., and Friston, K. J.: Mechanisms of evoked and induced responses in MEG/EEG. Neuroimage \textbf{31}(4), 1580--1591 (2006)
\bibitem{2014analyzing} Cohen, M. X., Analyzing neural time series data: theory and practice, (2014)
\bibitem{2018extracting} Wang, D. Q., Zhu, Y. J., Ristaniemi, T., and Cong, F. Y.: Extracting multi-mode ERP features using fifth-order nonnegative tensor decomposition. Journal of Neuroscience Methods, 240--247 (2018)
\end{thebibliography}
