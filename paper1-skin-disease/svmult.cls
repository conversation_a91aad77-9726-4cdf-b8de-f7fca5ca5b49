% SVMULT DOCUMENT CLASS -- version 5.11 (15-March-24)
% Springer Verlag global LaTeX2e support for multi authored books
%%
%%
%% \CharacterTable
%%  {Upper-case    \A\B\C\D\E\F\G\H\I\J\K\L\M\N\O\P\Q\R\S\T\U\V\W\X\Y\Z
%%   Lower-case    \a\b\c\d\e\f\g\h\i\j\k\l\m\n\o\p\q\r\s\t\u\v\w\x\y\z
%%   Digits        \0\1\2\3\4\5\6\7\8\9
%%   Exclamation   \!     Double quote  \"     Hash (number) \#
%%   Dollar        \$     Percent       \%     Ampersand     \&
%%   Acute accent  \'     Left paren    \(     Right paren   \)
%%   Asterisk      \*     Plus          \+     Comma         \,
%%   Minus         \-     Point         \.     Solidus       \/
%%   Colon         \:     Semicolon     \;     Less than     \<
%%   Equals        \=     Greater than  \>     Question mark \?
%%   Commercial at \@     Left bracket  \[     Backslash     \\
%%   Right bracket \]     Circumflex    \^     Underscore    \_
%%   Grave accent  \`     Left brace    \{     Vertical bar  \|
%%   Right brace   \}     Tilde         \~}
%%
\NeedsTeXFormat{LaTeX2e}[1995/12/01]
\ProvidesClass{svmult}[2024/03/15 v5.11
^^JSpringer Verlag global LaTeX document class for multi authored books]
% Options
% citations
\DeclareOption{natbib}{\ExecuteOptions{oribibl}%
\AtEndOfClass{% Loading package 'NATBIB'
\RequirePackage{natbib}
% Changing some parameters of NATBIB
\setlength{\bibhang}{\parindent}
%\setlength{\bibsep}{0mm}
\let\bibfont=\small
\def\@biblabel#1{#1.}
\newcommand{\etal}{\textit{et al}.}
%\bibpunct[,]{(}{)}{;}{a}{}{,}}}
}}
% Springer environment
\let\if@spthms\iftrue
\DeclareOption{nospthms}{\let\if@spthms\iffalse}
%
\let\envankh\@empty   % no anchor for "theorems"
%
\let\if@envcntreset\iffalse % environment counter is not reset
\let\if@envcntresetsect=\iffalse % reset each section
\DeclareOption{envcountresetchap}{\let\if@envcntreset\iftrue}
\DeclareOption{envcountresetsect}{\let\if@envcntreset\iftrue
\let\if@envcntresetsect=\iftrue}
%
\let\if@envcntsame\iffalse  % NOT all environments work like "Theorem",
                            % each using its own counter
\DeclareOption{envcountsame}{\let\if@envcntsame\iftrue}
%
\let\if@envcntshowhiercnt=\iffalse % do not show hierarchy counter at all
%
% enhance theorem counter
\DeclareOption{envcountchap}{\def\envankh{chapter}% show \thechapter along with theorem number
\let\if@envcntshowhiercnt=\iftrue}
%
\DeclareOption{envcountsect}{\def\envankh{section}% show \thesection along with theorem number
\let\if@envcntshowhiercnt=\iftrue
\ExecuteOptions{envcountresetsect}}
% reset environment counters every new contribution by default
\ExecuteOptions{envcountresetchap}
%
% languages
\let\switcht@@therlang\relax
\let\svlanginfo\relax
\def\ds@deutsch{\def\switcht@@therlang{\switcht@deutsch}%
\gdef\svlanginfo{\typeout{Man spricht deutsch.}\global\let\svlanginfo\relax}}
\def\ds@francais{\def\switcht@@therlang{\switcht@francais}%
\gdef\svlanginfo{\typeout{On parle francais.}\global\let\svlanginfo\relax}}
\def\ds@italiano{\def\switcht@@therlang{\switcht@italian}%
\gdef\svlanginfo{\typeout{Noi parliamo italiano.}\global\let\svlanginfo\relax}}
%
\AtBeginDocument{\@ifundefined{url}{\def\url#1{#1}}{}%
\@ifpackageloaded{babel}{%
\@ifundefined{extrasamerican}{}{\addto\extrasamerican{\switcht@albion}}%
\@ifundefined{extrasaustralian}{}{\addto\extrasaustralian{\switcht@albion}}%
\@ifundefined{extrasbritish}{}{\addto\extrasbritish{\switcht@albion}}%
\@ifundefined{extrascanadian}{}{\addto\extrascanadian{\switcht@albion}}%
\@ifundefined{extrasenglish}{}{\addto\extrasenglish{\switcht@albion}}%
\@ifundefined{extrasnewzealand}{}{\addto\extrasnewzealand{\switcht@albion}}%
\@ifundefined{extrasUKenglish}{}{\addto\extrasUKenglish{\switcht@albion}}%
\@ifundefined{extrasUSenglish}{}{\addto\extrasUSenglish{\switcht@albion}}%
\@ifundefined{captionsfrench}{}{\addto\captionsfrench{\switcht@francais}}%
\@ifundefined{extrasgerman}{}{\addto\extrasgerman{\switcht@deutsch}}%
\@ifundefined{extrasngerman}{}{\addto\extrasngerman{\switcht@deutsch}}%
\@ifundefined{captionsitalian}{}{\addto\captionsitalian{\switcht@italian}}%
}{\switcht@@therlang}%
}
\DeclareOption{italian}{\bbl@load@language{italian}}%
% numbering style of floats, equations
% \newif\if@numart   \@numartfalse
% \DeclareOption{numart}{\@numarttrue}
% numbering of headings
\let\if@chapnum=\iftrue
\def\nixchapnum{\let\if@chapnum\iffalse}
\def\numstyle{0}
\DeclareOption{nosecnum}{\def\numstyle{1}}%
% \DeclareOption{nochapnum}{\def\numstyle{2}}%
% \DeclareOption{nonum}{\def\numstyle{3}}%
\def\set@numbering{\ifcase\numstyle %\if@numart\else\num@book\fi %default
\or % 1-case - no \section-numbers
\setcounter{secnumdepth}{0}% \if@numart\else\num@book\fi
% \or % 2-case
% % chapter not numbered, but \sections are
% \def\thesection{\@arabic\c@section}%
% \nixchapnum
% \or % 3-case
% % neither chapter nor sections numbered + "numart"
% \nixchapnum
% \setcounter{secnumdepth}{0}%
\else\fi}
\AtEndOfClass{\set@numbering}
% style for vectors
\DeclareOption{vecphys}{\def\vec@style{phys}}
\DeclareOption{vecarrow}{\def\vec@style{arrow}}
% running heads
\let\if@runhead\iftrue
\DeclareOption{norunningheads}{\let\if@runhead\iffalse}
% referee option
\let\if@referee\iffalse
\def\makereferee{\def\baselinestretch{2}\selectfont
\newbox\refereebox
\setbox\refereebox=\vbox to\z@{\vskip0.5cm%
  \hbox to\textwidth{\normalsize\tt\hrulefill\lower0.5ex
        \hbox{\kern5\p@ referee's copy\kern5\p@}\hrulefill}\vss}%
\def\@oddfoot{\copy\refereebox}\let\@evenfoot=\@oddfoot}
\DeclareOption{referee}{\let\if@referee\iftrue
\AtBeginDocument{\makereferee\small\normalsize}}
% modification of thebibliography
\let\if@openbib\iffalse
\DeclareOption{openbib}{\let\if@openbib\iftrue}
% LaTeX standard, sectionwise references
\DeclareOption{oribibl}{\let\oribibl=Y}
\DeclareOption{chaprefs}{\let\chpbibl=Y}
%
% footinfo option (provides an informatory line on every page)
\def\SpringerMacroPackageNameA{svmult.cls}
% \thetime, \thedate and \timstamp are macros to include
% time, date (or both) of the TeX run in the document
\def\maketimestamp{\count255=\time
\divide\count255 by 60\relax
\edef\thetime{\the\count255:}%
\multiply\count255 by-60\relax
\advance\count255 by\time
\edef\thetime{\thetime\ifnum\count255<10 0\fi\the\count255}
\edef\thedate{\number\day-\ifcase\month\or Jan\or Feb\or Mar\or
             Apr\or May\or Jun\or Jul\or Aug\or Sep\or Oct\or
             Nov\or Dec\fi-\number\year}
\def\timstamp{\hbox to\hsize{\tt\hfil\thedate\hfil\thetime\hfil}}}
\maketimestamp
%
% \footinfo generates a info footline on every page containing
% pagenumber, jobname, macroname, and timestamp
\DeclareOption{footinfo}{\AtBeginDocument{\maketimestamp
   \def\ps@empty{\let\@mkboth\@gobbletwo
   \let\@oddhead\@empty\let\@evenhead\@empty}%
   \def\@oddfoot{\scriptsize\tt Page:\,\thepage\space\hfil
                 job:\,\jobname\space\hfil
                 macro:\,\SpringerMacroPackageNameA\space\hfil
                 date/time:\,\thedate/\thetime}%
   \let\@evenfoot=\@oddfoot}}
%
% start new chapter on any page
\newif\if@openright \@openrighttrue
\DeclareOption{openany}{\@openrightfalse}
%
% no size changing allowed
\DeclareOption{11pt}{\OptionNotUsed}
\DeclareOption{12pt}{\OptionNotUsed}
% options for the article class
\def\@rticle@options{10pt,twoside}
% fleqn
\DeclareOption{fleqn}{\def\@rticle@options{10pt,twoside,fleqn}%
\AtEndOfClass{\let\leftlegendglue\relax}%
\AtBeginDocument{\mathindent\parindent}}
% hanging sectioning titles
\let\if@sechang\iftrue
\DeclareOption{nosechang}{\let\if@sechang\iffalse}
% hanging sectioning titles
\def\ClassInfoNoLine#1#2{%
   \ClassInfo{#1}{#2\@gobble}%
}
%
\DeclareOption{graybox}{%
\AtEndOfClass{% Loading color package
\RequirePackage{color}%
% defining values of gray
\definecolor{shadecolor}{gray}{.85}%
\definecolor{tintedcolor}{gray}{.80}%
\RequirePackage{framed}%
%
\newenvironment{tinted}{%
  \def\FrameCommand{\colorbox{tintedcolor}}%
  \MakeFramed {\FrameRestore}}%
 {\endMakeFramed}%
%
\renewenvironment{svgraybox}%
       {\fboxsep=12pt\relax
        \begin{shaded}%
        \list{}{
leftmargin=12pt\rightmargin=2\leftmargin\leftmargin=\z@\topsep=\z@\relax}%
        \expandafter\item\parindent=\svparindent
        \hskip-\listparindent}%
       {\endlist\end{shaded}}%
%
\renewenvironment{svtintedbox}%
       {\fboxsep=12pt\relax
        \begin{tinted}%
        \list{}{
leftmargin=12pt\rightmargin=2\leftmargin\leftmargin=\z@\topsep=\z@\relax}%
        \expandafter\item\parindent=\svparindent
        \relax}%
       {\endlist\end{tinted}}%
%
}}
%
\let\SVMultOpt\@empty
\DeclareOption*{\InputIfFileExists{sv\CurrentOption.clo}{%
\global\let\SVMultOpt\CurrentOption}{%
\ClassWarning{Springer-SVMult}{Specified option or subpackage
"\CurrentOption" \MessageBreak not found -
passing it to article class \MessageBreak
-}\PassOptionsToClass{\CurrentOption}{article}%
}}
\ProcessOptions\relax
\ifx\SVMultOpt\@empty\relax
\ClassInfoNoLine{Springer-SVMult}{extra/valid Springer sub-package
\MessageBreak not found in option list - using "global" style}{}
\fi
\LoadClass[\@rticle@options]{article}
\raggedbottom

% various sizes and settings for contributed works

\setlength{\textwidth}{117mm}
%\setlength{\textheight}{12pt}\multiply\textheight by 45\relax
\setlength{\textheight}{191mm}
\setlength{\topmargin}{0cm}
\setlength\oddsidemargin   {63\p@}
\setlength\evensidemargin  {63\p@}
\setlength\marginparwidth{90\p@}
\setlength\headsep   {12\p@}

\newdimen\svparindent
\setlength{\svparindent}{12\p@}
\parindent\svparindent

\newdimen\bibindent
\setlength\bibindent{\parindent}

\setlength{\parskip}{\z@ \@plus \p@}
\setlength{\hfuzz}{2\p@}
\setlength{\arraycolsep}{1.5\p@}

\frenchspacing

\tolerance=500

\predisplaypenalty=0
\clubpenalty=10000
\widowpenalty=10000

\setlength\footnotesep{7.7\p@}

\newdimen\betweenumberspace          % dimension for space between
\betweenumberspace=5\p@              % number and text of titles
\newdimen\headlineindent             % dimension for space of
\headlineindent=2.5cc                % number and gap of running heads

% fonts, sizes, and the like
\renewcommand\normalsize{% 
   \@setfontsize\normalsize\@xpt\@xiipt
   \abovedisplayskip 10\p@ % \@plus2\p@ \@minus5\p@
   \abovedisplayshortskip \z@ % \@plus3\p@
   \belowdisplayshortskip 6\p@ %\@plus3\p@ \@minus3\p@
   \belowdisplayskip \abovedisplayskip
   \let\@listi\@listI}
\normalsize
\renewcommand\small{% 
   \@setfontsize\small{8.5}{10}%
   \abovedisplayskip 8.5\p@ % \@plus3\p@ \@minus4\p@
   \abovedisplayshortskip \z@ %\@plus2\p@
   \belowdisplayshortskip 4\p@ %\@plus2\p@ \@minus2\p@
   \def\@listi{\leftmargin\leftmargini
               \parsep \z@ \@plus\p@ \@minus\p@
               \topsep 6\p@ \@plus2\p@ \@minus4\p@
               \itemsep\z@}%
   \belowdisplayskip \abovedisplayskip
}
%
\let\footnotesize=\small
%
\renewcommand\Large{\@setfontsize\large{14}{16}}
\newcommand\LArge{\@setfontsize\Large{16}{18}}
\renewcommand\LARGE{\@setfontsize\LARGE{18}{20}}
%
\newenvironment{petit}{\par\addvspace{6\p@}\small}{\par\addvspace{6\p@}}
%

% modification of automatic positioning of floating objects
\setlength\@fptop{\z@ }
\setlength\@fpsep{12\p@ }
\setlength\@fpbot{\z@ \@plus 1fil }
\def\textfraction{.01}
\def\floatpagefraction{.8}
\setlength{\intextsep}{20\p@ \@plus 2\p@ \@minus 2\p@}
\setlength\textfloatsep{24\p@ \@plus 2\p@ \@minus 4\p@}
\setcounter{topnumber}{4}
\def\topfraction{.9}
\setcounter{bottomnumber}{2}
\def\bottomfraction{.7}
\setcounter{totalnumber}{6}
%
% size and style of headings
\newcommand{\partnumsize}{\LArge}
\newcommand{\partnumstyle}{\bfseries\boldmath}
\newcommand{\partsize}{\LARGE}
\newcommand{\partstyle}{\bfseries\boldmath}
\newcommand{\chapnumsize}{\Large}
\newcommand{\chapnumstyle}{\bfseries\boldmath}
\newcommand{\chapsize}{\LArge}
\newcommand{\chapstyle}{\bfseries\boldmath}
\newcommand{\chapauthsize}{\normalsize}
\newcommand{\chapauthstyle}{\bfseries\boldmath}
\newcommand{\mottosize}{\small}
\newcommand{\mottostyle}{\itshape\unboldmath\raggedright}
\newcommand{\secsize}{\large}
\newcommand{\secstyle}{\bfseries\boldmath}
%%\newcommand{\subsecsize}{\large}
\newcommand{\subsecsize}{\fontsize{11}{13}\selectfont}
\newcommand{\subsecstyle}{\bfseries\boldmath}
\newcommand{\subsubsecstyle}{\bfseries\boldmath}
%
\def\cleardoublepage{\clearpage\if@twoside \ifodd\c@page\else
    \hbox{}\newpage\if@twocolumn\hbox{}\newpage\fi\fi\fi}

\newcommand{\clearemptydoublepage}{%
        \clearpage{\pagestyle{empty}\cleardoublepage}}
\newcommand{\startnewpage}{\if@openright\clearemptydoublepage\else\clearpage\fi}

% MiniTOC
% one outputstream for all minitocs
\newwrite\minitoc
\let\MiniTOC=N % switch for MT processing in .aux files
\newcounter{minitocdepth}
\setcounter{minitocdepth}{0}

% stolen from LaTeX.ltx - read miniTOC and redirect output stream
\long\def \protected@immwrite#1#2#3{% 
      \begingroup
       \let\thepage\relax
       #2%
       \let\protect\@unexpandable@protect
       \edef\reserved@a{\immediate\write#1{#3}}%
       \reserved@a
      \endgroup
      \if@nobreak\ifvmode\nobreak\fi\fi}
%
\newcommand{\@mtstarttoc}[1]
{\begingroup
 \makeatletter
 \immediate\write\@auxout{\string\immediate\string\closeout\string\minitoc}%
 \typeout{input jobname.#1}%
\small
 \@input{\jobname.#1}%
 \protected@immwrite\@auxout
   {\let\label\@gobble \let\index\@gobble
    \let\glossary\@gobble}%
   {\immediate\openout\minitoc \jobname.#1\relax}
 \global\@nobreakfalse\endgroup}
%
\newcommand{\@mtstarttocquiet}[1]
{\begingroup
 \makeatletter
 \protected@write\@auxout
   {\let\label\@gobble \let\index\@gobble
    \let\glossary\@gobble}%
   {\immediate\openout\minitoc \jobname.#1\relax}
 \global\@nobreakfalse\endgroup}
%
\newcommand{\mtaddtocont}[1]
{\protected@write \@auxout
  {\let\label\@gobble \let\index\@gobble
   \let\glossary\@gobble}%
  {\string\@mtwritefile{#1}}}
%
\newcommand{\@mtwritefile}[1]{\if Y\MiniTOC
\@temptokena{#1} \immediate\write\minitoc{\the\@temptokena}\fi}

\AtEndDocument{\immediate\write\@auxout{\string\immediate\string\closeout\string\minitoc}}

\newcommand{\dominitoc}{% switch \let\MiniTOC=Y
    \protected@immwrite\@auxout{}{
let\MiniTOC=Y}%
    \ifnum \c@minitocdepth<1
        \@mtstarttocquiet{t\thecontribution}\relax
    \else
        \@mtstarttoc{t\thecontribution}\par\addvspace\bigskipamount
    \fi}

% redefinition of \part
\renewcommand\part{\clearemptydoublepage
         \thispagestyle{empty}
         \if@twocolumn
            \onecolumn
            \@tempswatrue
         \else
            \@tempswafalse
         \fi
         \@ifundefined{thispagecropped}{}{
thispagecropped}
         \secdef\@part\@spart}

\def\@part[#1]#2{\ifnum \c@secnumdepth >-2\relax
        \refstepcounter{part}
        \addcontentsline{toc}{part}{\partname\
        \thepart\thechapterend\hspace{\betweenumberspace}%
        #1}\else
        \addcontentsline{toc}{part}{#1}\fi
   \markboth{}{}
   {\raggedleft
    \hyphenpenalty \@M
    \interlinepenalty\@M
    \ifnum \c@secnumdepth >-2\relax
      \normalfont\partnumsize\partnumstyle %\vrule height 34pt width 0pt depth 0pt%
     \partname\ \thepart %\llap{\smash{\lower 5pt\hbox to\textwidth{\hrulefill}}}
    \par
    \vskip 2\p@ \fi
    \partsize\partstyle #2\par}\@endpart}
%
% \@endpart finishes the part page
%
\def\@endpart{\vfil\newpage
   \if@twoside
       \hbox{}
       \thispagestyle{empty}
       \newpage
   \fi
   \if@tempswa
     \twocolumn
   \fi}
%
\def\@spart#1{{\raggedleft
   \normalfont\partsize\partstyle
   #1\par}\@endpart}
%
\newenvironment{partbacktext}{\def\@endpart{\vfil\newpage}}
{\thispagestyle{empty} \newpage}
%
% (re)define sectioning
\setcounter{secnumdepth}{3}

\def\seccounterend{}
\def\seccountergap{\hskip\betweenumberspace}
\def\@seccntformat#1{\csname the#1\endcsname\seccounterend\seccountergap\ignorespaces}
%
\let\firstmark=\botmark
%
\@ifundefined{thechapterend}{\def\thechapterend{}}{}
%
\if@sechang
   \def\sec@hangfrom#1{\setbox\@tempboxa\hbox{#1}%
         \hangindent\wd\@tempboxa\noindent\box\@tempboxa}
\else
   \def\sec@hangfrom#1{\setbox\@tempboxa\hbox{#1}%
         \hangindent\z@\noindent\box\@tempboxa}
\fi

\def\chap@hangfrom#1{\if!#1!\else
\@chapapp\ #1\vskip2pt\fi}
\def\schap@hangfrom{\chap@hangfrom{}}

\newcounter{chapter}

\newif\if@mainmatter \@mainmattertrue
\newcommand\frontmatter{\startnewpage
            \@mainmatterfalse\pagenumbering{roman}
            \setcounter{page}{5}}
%
\newcommand\mainmatter{\clearemptydoublepage
            \@mainmattertrue
            \markboth{}{}
            \pagenumbering{arabic}}
%
\newcommand\backmatter{% 
\setcounter{minitocdepth}{0}%
\pagestyle{headings}%
\clearemptydoublepage %\@mainmatterfalse
\let\appendix=\bppendix
\def\bibsection{\chapter*{\refname}\@mkboth{\refname}{\refname}%
     \addcontentsline{toc}{chapter}{\refname}%
     \csname biblst@rthook\endcsname\par}%
}

\renewenvironment{titlepage}
    {%
      \cleardoublepage
      \if@twocolumn
        \@restonecoltrue\onecolumn
      \else
        \@restonecolfalse\newpage
      \fi
      \thispagestyle{empty}%
      \addtocounter{page}\m@ne
  \def\and{\unskip, }
  \parindent=\z@
  \pretolerance=10000
  \rightskip=0pt plus 1fil
  \large                    % default size for titlepage
  \vspace*{2em}             % Vertical space above title.
 }{{\LARGE                   % each author set in \LARGE
   \lineskip .5em
   \<AUTHOR>
  \vskip 2cm                % Vertical space after author.
  {\Huge\bfseries\@title \par}% Title set in \Huge size and bold face
  \vskip 1cm                % Vertical space after title.
  \if!\@subtitle!\else
   {\LARGE\ignorespaces\@subtitle \par}
   \vskip 1cm               % Vertical space after subtitle.
  \fi
  \if!\@date!\else
    \@date
    \par
    \vskip 1.5em            % Vertical space after date.
  \fi
 \vfill
 {\Large\bfseries Springer\par}
%\vskip 3pt
%\itshape
%  Berlin\enspace Heidelberg\enspace New\kern0.1em York\\
%  Hong\kern0.2em Kong\enspace London\\
%  Milan\enspace Paris\enspace Tokyo\par
     \if@restonecol\twocolumn \else \newpage \fi
     \if@twoside\else
        \setcounter{page}\@ne
     \fi
 \clearheadinfo
}

\def\@chapapp{\chaptername}

\newdimen\mottowidth
\newcommand\motto[2][77mm]{%
\setlength{\mottowidth}{#1}%
\gdef\m@ttotext{#2}}
%
\newcommand{\processmotto}{\@ifundefined{m@ttotext}{}{%
    \setbox0=\hbox{\vbox{\hyphenpenalty=50
    \begin{flushright}
    \begin{minipage}{\mottowidth}
       \vrule\@width\z@\@height21\p@\@depth\z@
       \normalfont\mottosize\mottostyle\m@ttotext
    \end{minipage}
    \end{flushright}}}%
    \@tempdima=\pagetotal
    \advance\@tempdima by\ht0
    \ifdim\@tempdima<157\p@
       \multiply\@tempdima by-1
       \advance\@tempdima by157\p@
       \vskip\@tempdima
    \fi
    \box0\par
    \global\let\m@ttotext=\undefined}}

\newcommand{\chapsubtitle}[1]{%
\gdef\ch@psubtitle{#1}}
%
\newcommand{\processchapsubtit}{\@ifundefined{ch@psubtitle}{}{%
    {\normalfont\chapnumsize\chapnumstyle
    \vskip 14\p@
    \ch@psubtitle
    \par}
    \global\let\ch@psubtitle=\undefined}}

\newcommand{\chapauthor}[1]{%
\gdef\ch@pauthor{#1}}
%
\newcommand{\processchapauthor}{\@ifundefined{ch@pauthor}{}{%
    {\normalfont\chapauthsize\chapauthstyle
    \vskip 20\p@
    \ch@pauthor
    \par}
    \global\let\ch@pauthor=\undefined}}

\newcommand\chapter{\startnewpage
                    \@ifundefined{thispagecropped}{}{
thispagecropped}
                    \thispagestyle{bchap}%
                    \if@chapnum\else
                       \begingroup
                         \let\@elt\@stpelt
                         \csname cl@chapter\endcsname
                       \endgroup
                    \fi
                    \global\@topnum\z@
                    \@afterindentfalse
                    \secdef\@chapter\@schapter}

\def\@chapter[#1]#2{\if@chapnum  % war mal \ifnum \c@secnumdepth >\m@ne
                       \refstepcounter{chapter}%
                       \if@mainmatter
                         \typeout{\@chapapp\space\thechapter.}%
                         \addcontentsline{toc}{chapter}{\protect
                                  \numberline{\thechapter\thechapterend}#1}%
                       \else
                         \addcontentsline{toc}{chapter}{#1}%
                       \fi
                    \else
                      \addcontentsline{toc}{chapter}{#1}%
                    \fi
                    \chaptermark{#1}%
                    \addtocontents{lof}{\protect\addvspace{10\p@}}%
                    \addtocontents{lot}{\protect\addvspace{10\p@}}%
                    \if@twocolumn
                      \@topnewpage[\@makechapterhead{#2}]%
                    \else
                      \@makechapterhead{#2}%
                      \@afterheading
                    \fi}

\def\@schapter#1{\if@twocolumn
                   \@topnewpage[\@makeschapterhead{#1}]%
                 \else
                   \@makeschapterhead{#1}%
                   \@afterheading
                 \fi}

%%changes position and layout of numbered chapter headings
\def\@makechapterhead#1{{\parindent\z@\raggedright\normalfont
  \hyphenpenalty \@M
  \interlinepenalty\@M
  \if@chapnum
     \chapnumsize\chapnumstyle
     \@chapapp\ \thechapter\thechapterend\par
     \vskip 2\p@
  \fi
  \chapsize\chapstyle
  \ignorespaces#1\par\nobreak
  \processchapsubtit
  \processchapauthor
  \processmotto
  \ifdim\pagetotal>167\p@
     \vskip 11\p@
  \else
     \@tempdima=167\p@\advance\@tempdima by-\pagetotal
     \vskip\@tempdima
  \fi}}

%%changes position and layout of unnumbered chapter headings
\def\@makeschapterhead#1{{\parindent \z@ \raggedright\normalfont
  \hyphenpenalty \@M
  \interlinepenalty\@M
  \chapsize\chapstyle
  \ignorespaces#1\par\nobreak
  \processmotto
  \ifdim\pagetotal>167\p@
     \vskip 11\p@
  \else
     \@tempdima=168\p@\advance\@tempdima by-\pagetotal
     \vskip\@tempdima
  \fi}}
%
% dedication environment
\newenvironment{dedication}
{\clearemptydoublepage
\thispagestyle{empty}
\vspace*{13\baselineskip}
\large\itshape
\let\\\@centercr\@rightskip\@flushglue \rightskip\@rightskip
\leftskip4cm\parindent\z@\relax
\everypar{\parindent=\svparindent\let\everypar\empty}}{\clearpage}
%
% predefined unnumbered headings
\newcommand{\preface}[1][\prefacename]{\chapter*{#1}\markboth{#1}{#1}}
\newcommand{\foreword}[1][\forewordname]{\chapter*{#1}\markboth{#1}{#1}}
\newcommand{\contributors}[1][\contriblistname]{\chapter*{#1}\markboth{#1}{#1}}
\newcommand{\extrachap}[1]{\chapter*{#1}\markboth{#1}{#1}}
% same with TOC entry
\newcommand{\Extrachap}[1]{\chapter*{#1}\markboth{#1}{#1}%
\addcontentsline{toc}{chapter}{#1}}

% measures and setting of sections
\renewcommand\section{\@startsection{section}{1}{\z@}%
                       {-30\p@}% \p@lus -4\p@ \@minus -4\p@}%
                       {16\p@}% \p@lus 4\p@ \@minus 4\p@}%
                       {\normalfont\secsize\secstyle
                        \rightskip=\z@ \@plus 8em\pretolerance=10000 }}
\renewcommand\subsection{\@startsection{subsection}{2}{\z@}%
                       {-30\p@}% \p@lus -4\p@ \@minus -4\p@}%
                       {16\p@}% \p@lus 4\p@ \@minus 4\p@}%
                       {\normalfont\subsecsize\subsecstyle
                        \rightskip=\z@ \@plus 8em\pretolerance=10000 }}
\renewcommand\subsubsection{\@startsection{subsubsection}{3}{\z@}%
                       {-24\p@}% \p@lus -4\p@ \@minus -4\p@}%
                       {12\p@}% \p@lus 4\p@ \@minus 4\p@}%
                       {\normalfont\normalsize\subsubsecstyle
                        \rightskip=\z@ \@plus 8em\pretolerance=10000 }}
\renewcommand\paragraph{\@startsection{paragraph}{4}{\z@}%
                       {-24\p@}% \p@lus -4\p@ \@minus -4\p@}%
                       {12\p@}% \p@lus 4\p@ \@minus 4\p@}%
                       {\normalfont\normalsize\upshape
                        \rightskip=\z@ \@plus 8em\pretolerance=10000 }}
\renewcommand\subparagraph{\@startsection{paragraph}{4}{\z@}%
                       {-18\p@}% \p@lus -4\p@ \@minus -4\p@}%
                       {6\p@}% \p@lus 4\p@ \@minus 4\p@}%
                       {\normalfont\normalsize\itshape
                        \rightskip=\z@ \@plus 8em\pretolerance=10000 }}
\newcommand\runinhead{\@startsection{paragraph}{4}{\z@}%
                       {-6\p@}% \p@lus -4\p@ \@minus -4\p@}%
                       {-6\p@}%
                       {\normalfont\normalsize\bfseries\boldmath
                        \rightskip=\z@ \@plus 8em\pretolerance=10000 }}
\newcommand\subruninhead{\@startsection{paragraph}{4}{\z@}%
                       {-6\p@}% \p@lus -4\p@ \@minus -4\p@}%
                       {-6\p@}%
                       {\normalfont\normalsize\bfseries\itshape
                        \rightskip=\z@ \@plus 8em\pretolerance=10000 }}

\newcommand\subsubruninhead{\@startsection{paragraph}{4}{\z@}%
                       {-6\p@}% \p@lus -4\p@ \@minus -4\p@}%
                       {0.1\p@}%
                       {\normalfont\normalsize\bfseries
                        \rightskip=\z@ \@plus 8em\pretolerance=10000 }}
                        
% Appendix
\renewcommand\appendix{\par}         %article appendix

\newcommand\bppendix{\startnewpage            %book appendix
                \pagestyle{headings}
                \stepcounter{chapter}
                \setcounter{chapter}{0}
                \stepcounter{section}
                \setcounter{section}{0}
                \setcounter{equation}{0}
                \setcounter{figure}{0}
                \setcounter{table}{0}
                \setcounter{footnote}{0}
\let\if@chapnum=\iftrue
\def\@chapapp{\appendixname}
\renewcommand\thechapter{\@Alph\c@chapter}
\renewcommand\thesection{\thechapter.\@arabic\c@section}
\renewcommand\thesubsection{\thesection.\@arabic\c@subsection}
\renewcommand\theequation{\thechapter.\@arabic\c@equation}
\renewcommand\thefigure{\thechapter.\@arabic\c@figure}
\renewcommand\thetable{\thechapter.\@arabic\c@table}}

\def\hyperhrefextend{\ifx\hyper@anchor\@undefined\else
{\@currentHref}\fi}

\def\runinsep{}
\def\aftertext{\unskip\runinsep}
%
\def\@ssect#1#2#3#4#5{%
  \@tempskipa #3\relax
  \ifdim \@tempskipa>\z@
    \begingroup
      #4{%
        \@hangfrom{\hskip #1}%
          \raggedright
          \hyphenpenalty \@M
          \interlinepenalty \@M #5\@@par}%
    \endgroup
  \else
    \def\@svsechd{#4{\hskip #1\relax #5}}%
  \fi
      \addcontentsline{toc}{section}{#5}%
  \@xsect{#3}}
%
\def\@sect#1#2#3#4#5#6[#7]#8{%
   \ifnum #2>\c@secnumdepth
      \let\@svsec\@empty
   \else
      \refstepcounter{#1}%
      \protected@edef\@svsec{\@seccntformat{#1}\relax}%
   \fi
   \@tempskipa #5\relax
   \ifdim \@tempskipa>\z@
      \begingroup #6\relax
         \sec@hangfrom{\hskip #3\relax\@svsec}%
         {\raggedright
          \hyphenpenalty \@M
          \interlinepenalty \@M #8\@@par}%
      \endgroup
      \csname #1mark\endcsname{#7}%
      \addcontentsline{toc}{#1}{%
        \ifnum #2>\c@secnumdepth \else
          \protect\numberline{\csname the#1\endcsname}%
        \fi
        #7}%
      \ifnum #2>\c@minitocdepth \else
         \mtaddtocont{\protect\contentsline
             \ifnum #2>\@ne{mtsec}\else{mtchap}\fi
             \ifnum #2>\c@secnumdepth
                {#7}%
             \else
                {\protect\numberline{\csname the#1\endcsname}#7}%
             \fi
             {\thepage}\hyperhrefextend}%
      \fi
   \else
      \def\@svsechd{%
         #6\hskip #3\relax
         \@svsec #8\aftertext\ignorespaces
         \csname #1mark\endcsname{#7}%
         \addcontentsline{toc}{#1}{%
            \ifnum #2>\c@secnumdepth \else
                \protect\numberline{\csname the#1\endcsname}%
            \fi
            #7}}%
   \fi
   \@xsect{#5}}

% figures and tables are processed in small print
\def \@floatboxreset {%
        \reset@font
        \small
        \@setnobreak
        \@setminipage
}
\def\fps@figure{htbp}
\def\fps@table{htbp}
%
% Frame for paste-in figures or tables
\def\mpicplace#1#2{%  #1 =width   #2 =height
\vbox{\hbox to #1{\vrule\@width \fboxrule \@height #2\hfill}}}
%
\newenvironment{svgraybox}%
       {\ClassWarning{Springer-SVMono}{Environment "svgraybox" not available,\MessageBreak
         switching over to "quotation" environment;\MessageBreak
         specify documentclass option "graybox",\MessageBreak
         see SVMono documentation -}%
                \par\addvspace{6pt}
                \list{}{\listparindent12\p@%
                        \leftmargin=12\p@%
                        \itemindent    \listparindent
                        \rightmargin   \leftmargin
                        \parsep        \z@ \@plus\p@}%
                \expandafter\item\parindent=\svparindent
                \relax\hskip-\listparindent}%
       {\endlist}%
%
\newenvironment{svtintedbox}%
       {\ClassWarning{Springer-SVMono}{Environment "svtintedbox" not available,\MessageBreak
         switching over to "quotation" environment;\MessageBreak
         specify documentclass option "graybox",\MessageBreak
         see SVMono documentation -}%
                \par\addvspace{6pt}
                \list{}{\listparindent12\p@%
                        \leftmargin=12\p@%
                        \itemindent    \listparindent
                        \rightmargin   \leftmargin
                        \parsep        \z@ \@plus\p@}%
                \expandafter\item\parindent=\svparindent
                \relax\hskip-\listparindent}%
       {\endlist}%
%
\renewenvironment{quotation}
               {\par\addvspace{6pt}
                \list{}{\listparindent12\p@%
                        \leftmargin=12\p@%
                        \itemindent    \listparindent
                        \rightmargin   \leftmargin
                        \parsep        \z@ \@plus\p@%
                        \small}%
                \item\relax\hskip-\listparindent}
               {\endlist}
%
\renewenvironment{quote}
               {\par\addvspace{6pt}
                \list{}{\leftmargin=12\p@%
                \rightmargin\leftmargin
                \parsep=3\p@
                \small}%
                \item\relax}
               {\endlist}

% labels of enumerate
\renewcommand\labelenumii{\theenumii.}
\renewcommand\theenumii{\@alph\c@enumii}

% labels of itemize
\renewcommand\labelitemi{\textbullet}
\renewcommand\labelitemii{\textendash}
\let\labelitemiii=\labelitemiv

% labels of description
\renewcommand*\descriptionlabel[1]{\hspace\labelsep #1\hfil}

% fixed indentation for standard itemize-environment
\newdimen\svitemindent \setlength{\svitemindent}{\parindent}


% make indentations changeable

\def\setitemindent#1{\settowidth{\labelwidth}{#1}%
        \let\setit@m=Y%
        \leftmargini\labelwidth
        \advance\leftmargini\labelsep
   \def\@listi{\leftmargin\leftmargini
        \labelwidth\leftmargini\advance\labelwidth by -\labelsep
        \parsep=\parskip
        \topsep=\medskipamount
        \itemsep=\parskip \advance\itemsep by -\parsep}}
\def\setitemitemindent#1{\settowidth{\labelwidth}{#1}%
        \let\setit@m=Y%
        \leftmarginii\labelwidth
        \advance\leftmarginii\labelsep
\def\@listii{\leftmargin\leftmarginii
        \labelwidth\leftmarginii\advance\labelwidth by -\labelsep
        \parsep=\parskip
        \topsep=6\p@
        \itemsep=\parskip \advance\itemsep by -\parsep}}
%
% adjusted environment "description"
% if an optional parameter (at the first two levels of lists)
% is present, its width is considered to be the widest mark
% throughout the current list.
\def\description{\@ifnextchar[{\@describe}{\list{}{\labelwidth\z@
\labelsep=12pt\relax  %!!!!!!!!!
\leftmargini=12pt\relax  %!!!!!!!!!
\leftmargin=12pt\relax  %!!!!!!!!!
          \itemindent-\leftmargin \let\makelabel\descriptionlabel}}}
%
\def\describelabel#1{#1\hfil}
\def\@describe[#1]{\labelsep=12pt\relax
\relax\ifnum\@listdepth=0
\setitemindent{#1}\else\ifnum\@listdepth=1
\setitemitemindent{#1}\fi\fi
\list{--}{\let\makelabel\describelabel}}
%
\def\itemize{%
  \ifnum \@itemdepth >\thr@@\@toodeep\else
    \advance\@itemdepth\@ne
    \ifx\setit@m\undefined
       \ifnum \@itemdepth=1 \leftmargini=\svitemindent
          \labelwidth\leftmargini\advance\labelwidth-\labelsep
          \leftmarginii=\leftmargini \leftmarginiii=\leftmargini
       \fi
    \fi
    \edef\@itemitem{labelitem\romannumeral\the\@itemdepth}%
    \expandafter\list
      \csname\@itemitem\endcsname
      {\def\makelabel##1{\rlap{##1}\hss}}%
  \fi}
%
\def\enumerate{%
  \ifnum \@enumdepth >\thr@@\@toodeep\else
    \advance\@enumdepth\@ne
    \ifx\setit@m\undefined
       \ifnum \@enumdepth=1 \leftmargini=\svitemindent
          \labelwidth\leftmargini\advance\labelwidth-\labelsep
          \leftmarginii=\leftmargini \leftmarginiii=\leftmargini
       \fi
    \fi
    \edef\@enumctr{enum\romannumeral\the\@enumdepth}%
      \expandafter
      \list
        \csname label\@enumctr\endcsname
        {\usecounter\@enumctr\def\makelabel##1{\hss\llap{##1}}}%
  \fi}
%
\newdimen\verbatimindent \verbatimindent\parindent
\def\verbatim{\advance\@totalleftmargin by\verbatimindent
\@verbatim \frenchspacing\@vobeyspaces \@xverbatim}

%
%  special signs and characters
\newcommand{\D}{\mathrm{d}}
\newcommand{\E}{\mathrm{e}}
\let\eul=\E
\newcommand{\I}{{\rm i}}
\let\imag=\I
%
% the definition of uppercase Greek characters
% Springer likes them as italics to depict variables
\DeclareMathSymbol{\Gamma}{\mathalpha}{letters}{"00}
\DeclareMathSymbol{\Delta}{\mathalpha}{letters}{"01}
\DeclareMathSymbol{\Theta}{\mathalpha}{letters}{"02}
\DeclareMathSymbol{\Lambda}{\mathalpha}{letters}{"03}
\DeclareMathSymbol{\Xi}{\mathalpha}{letters}{"04}
\DeclareMathSymbol{\Pi}{\mathalpha}{letters}{"05}
\DeclareMathSymbol{\Sigma}{\mathalpha}{letters}{"06}
\DeclareMathSymbol{\Upsilon}{\mathalpha}{letters}{"07}
\DeclareMathSymbol{\Phi}{\mathalpha}{letters}{"08}
\DeclareMathSymbol{\Psi}{\mathalpha}{letters}{"09}
\DeclareMathSymbol{\Omega}{\mathalpha}{letters}{"0A}
% the upright forms are defined here as \var<Character>
\DeclareMathSymbol{\varGamma}{\mathalpha}{operators}{"00}
\DeclareMathSymbol{\varDelta}{\mathalpha}{operators}{"01}
\DeclareMathSymbol{\varTheta}{\mathalpha}{operators}{"02}
\DeclareMathSymbol{\varLambda}{\mathalpha}{operators}{"03}
\DeclareMathSymbol{\varXi}{\mathalpha}{operators}{"04}
\DeclareMathSymbol{\varPi}{\mathalpha}{operators}{"05}
\DeclareMathSymbol{\varSigma}{\mathalpha}{operators}{"06}
\DeclareMathSymbol{\varUpsilon}{\mathalpha}{operators}{"07}
\DeclareMathSymbol{\varPhi}{\mathalpha}{operators}{"08}
\DeclareMathSymbol{\varPsi}{\mathalpha}{operators}{"09}
\DeclareMathSymbol{\varOmega}{\mathalpha}{operators}{"0A}
% Upright Lower Case Greek letters without using a new MathAlphabet
\newcommand{\greeksym}[1]{\usefont{U}{psy}{m}{n}#1}
\newcommand{\greeksymbold}[1]{{\usefont{U}{psy}{b}{n}#1}}

\newcommand{\allmodesymb}[2]{\relax\ifmmode{\mathchoice
{\mbox{\fontsize{\tf@size}{\tf@size}#1{#2}}}
{\mbox{\fontsize{\tf@size}{\tf@size}#1{#2}}}
{\mbox{\fontsize{\sf@size}{\sf@size}#1{#2}}}
{\mbox{\fontsize{\ssf@size}{\ssf@size}#1{#2}}}}
\else
\mbox{#1{#2}}\fi}
% Definition of lower case Greek letters
\newcommand{\ualpha}{\allmodesymb{\greeksym}{a}}
\newcommand{\ubeta}{\allmodesymb{\greeksym}{b}}
\newcommand{\uchi}{\allmodesymb{\greeksym}{c}}
\newcommand{\udelta}{\allmodesymb{\greeksym}{d}}
\newcommand{\ugamma}{\allmodesymb{\greeksym}{g}}
\newcommand{\umu}{\allmodesymb{\greeksym}{m}}
\newcommand{\unu}{\allmodesymb{\greeksym}{n}}
\newcommand{\upi}{\allmodesymb{\greeksym}{p}}
\newcommand{\utau}{\allmodesymb{\greeksym}{t}}
% redefines the \vec accent to a bold character - if desired
\def\vec@type{arrow}% temporarily abused
\AtBeginDocument{\ifx\vec@style\vec@type\else
\@ifundefined{vec@style}{%
 \def\vec#1{\ensuremath{\mathchoice
                     {\mbox{\boldmath$\displaystyle\mathbf{#1}$}}
                     {\mbox{\boldmath$\textstyle\mathbf{#1}$}}
                     {\mbox{\boldmath$\scriptstyle\mathbf{#1}$}}
                     {\mbox{\boldmath$\scriptscriptstyle\mathbf{#1}$}}}}%
}
{\def\vec#1{\ensuremath{\mathchoice
                     {\mbox{\boldmath$\displaystyle#1$}}
                     {\mbox{\boldmath$\textstyle#1$}}
                     {\mbox{\boldmath$\scriptstyle#1$}}
                     {\mbox{\boldmath$\scriptscriptstyle#1$}}}}%
}
\fi}
% tensor
\def\tens#1{\relax\ifmmode\mathsf{#1}\else\textsf{#1}\fi}

% end of proof symbol
\newcommand\qedsymbol{\hbox{\rlap{$\sqcap$}$\sqcup$}}
\newcommand\qed{\relax\ifmmode\else\unskip\quad\fi\qedsymbol}
\newcommand\smartqed{\renewcommand\qed{\relax\ifmmode\qedsymbol\else
  {\unskip\nobreak\hfil\penalty50\hskip1em\null\nobreak\hfil\qedsymbol
  \parfillskip=\z@\finalhyphendemerits=0\endgraf}\fi}}
%
\newif\if@numart   \@numarttrue
\def\ds@numart{\@numarttrue
  \@takefromreset{figure}{chapter}%
  \@takefromreset{table}{chapter}%
  \@takefromreset{equation}{chapter}%
  \def\thesection{\@arabic\c@section}%
  \def\thefigure{\@arabic\c@figure}%
  \def\thetable{\@arabic\c@table}%
  \def\theequation{\arabic{equation}}%
  \def\thesubequation{\arabic{equation}\alph{subequation}}}
%
\def\ds@book{\@numartfalse
\def\thesection{\thechapter.\@arabic\c@section}%
\def\thefigure{\thechapter.\@arabic\c@figure}%
\def\thetable{\thechapter.\@arabic\c@table}%
\def\theequation{\thechapter.\arabic{equation}}%
\@addtoreset{section}{chapter}%
\@addtoreset{figure}{chapter}%
\@addtoreset{table}{chapter}%
\@addtoreset{equation}{chapter}}
%
% Ragged bottom for the actual page
\def\thisbottomragged{\def\@textbottom{\vskip\z@ \@plus.0001fil
\global\let\@textbottom\relax}}

% This is texte.tex
% it defines various texts and their translations
% called up with documentstyle options
\def\switcht@albion{%
\def\abbrsymbname{List of Abbreviations and Symbols}%
\def\abstractname{Abstract}%
\def\ackname{Acknowledgements}%
\def\andname{and}%
\def\bibname{References}%
\def\lastandname{, and}%
\def\appendixname{Appendix}%
\def\chaptername{Chapter}%
\def\claimname{Claim}%
\def\conjecturename{Conjecture}%
\def\contentsname{Contents}%
\def\corollaryname{Corollary}%
\def\definitionname{Definition}%
\def\emailname{e-mail}%
\def\examplename{Example}%
\def\exercisename{Exercise}%
\def\figurename{Fig.}%
\def\forewordname{Foreword}%
\def\keywordname{{\bf Key words:}}%
\def\indexname{Index}%
\def\lemmaname{Lemma}%
\def\contriblistname{List of Contributors}%
\def\listfigurename{List of Figures}%
\def\listtablename{List of Tables}%
\def\mailname{{\it Correspondence to\/}:}%
\def\noteaddname{Note added in proof}%
\def\notename{Note}%
\def\partname{Part}%
\def\prefacename{Preface}%
\def\problemname{Problem}%
\def\proofname{Proof}%
\def\propertyname{Property}%
\def\propositionname{Proposition}%
\def\questionname{Question}%
\def\refname{References}%
\def\remarkname{Remark}%
\def\seename{see}%
\def\solutionname{Solution}%
\def\subclassname{{\it Subject Classifications\/}:}%
\def\tablename{Table}%
\def\theoremname{Theorem}}
\switcht@albion
% Names of theorem like environments are already defined
% but must be translated if another language is chosen
%
% French section
\def\switcht@francais{\svlanginfo
 \def\abbrsymbname{Liste des abbr\'eviations et symboles}%
 \def\abstractname{R\'esum\'e.}%
 \def\ackname{Remerciements.}%
 \def\andname{et}%
 \def\lastandname{ et}%
 \def\appendixname{Appendice}%
 \def\bibname{Bibliographie}%
 \def\chaptername{Chapitre}%
 \def\claimname{Pr\'etention}%
 \def\conjecturename{Hypoth\`ese}%
 \def\contentsname{Table des mati\`eres}%
 \def\corollaryname{Corollaire}%
 \def\definitionname{D\'efinition}%
 \def\emailname{e-mail}%
 \def\examplename{Exemple}%
 \def\exercisename{Exercice}%
 \def\figurename{Fig.}%
 \def\forewordname{Avant-propos}%
 \def\keywordname{{\bf Mots-cl\'e:}}%
 \def\indexname{Index}%
 \def\lemmaname{Lemme}%
 \def\contriblistname{Liste des contributeurs}%
 \def\listfigurename{Liste des figures}%
 \def\listtablename{Liste des tables}%
 \def\mailname{{\it Correspondence to\/}:}%
 \def\noteaddname{Note ajout\'ee \`a l'\'epreuve}%
 \def\notename{Remarque}%
 \def\partname{Partie}%
 \def\prefacename{Pr\'eface}%
 \def\problemname{Probl\`eme}%
 \def\proofname{Preuve}%
 \def\propertyname{Caract\'eristique}%
%\def\propositionname{Proposition}%
 \def\questionname{Question}%
 \def\refname{Litt\'erature}%
 \def\remarkname{Remarque}%
 \def\seename{voir}%
 \def\solutionname{Solution}%
 \def\subclassname{{\it Subject Classifications\/}:}%
 \def\tablename{Tableau}%
 \def\theoremname{Th\'eor\`eme}%
}
%
% German section
\def\switcht@deutsch{\svlanginfo
 \def\abbrsymbname{Abk\"urzungs- und Symbolverzeichnis}%
 \def\abstractname{Zusammenfassung}%
 \def\ackname{Danksagung}%
 \def\andname{und}%
 \def\lastandname{ und}%
 \def\appendixname{Anhang}%
 \def\bibname{Literaturverzeichnis}%
 \def\chaptername{Kapitel}%
 \def\claimname{Behauptung}%
 \def\conjecturename{Hypothese}%
 \def\contentsname{Inhaltsverzeichnis}%
 \def\corollaryname{Korollar}%
%\def\definitionname{Definition}%
 \def\emailname{E-Mail}%
 \def\examplename{Beispiel}%
 \def\exercisename{"Ubung}%
 \def\figurename{Abb.}%
 \def\forewordname{Geleitwort}%
 \def\keywordname{{\bf Schl\"usselw\"orter:}}%
 \def\indexname{Sachverzeichnis}%
%\def\lemmaname{Lemma}%
 \def\contriblistname{Mitarbeiter}%
 \def\listfigurename{Abbildungsverzeichnis}%
 \def\listtablename{Tabellenverzeichnis}%
 \def\mailname{{\it Correspondence to\/}:}%
 \def\noteaddname{Nachtrag}%
 \def\notename{Anmerkung}%
 \def\partname{Teil}%
 \def\prefacename{Vorwort}%
%\def\problemname{Problem}%
 \def\proofname{Beweis}%
 \def\propertyname{Eigenschaft}%
%\def\propositionname{Proposition}%
 \def\questionname{Frage}%
 \def\refname{Literaturverzeichnis}%
 \def\remarkname{Anmerkung}%
 \def\seename{siehe}%
 \def\solutionname{L\"osung}%
 \def\subclassname{{\it Subject Classifications\/}:}%
 \def\tablename{Tabelle}%
%\def\theoremname{Theorem}%
}

% Italian section
\def\switcht@italian{\svlanginfo
\def\abstractname{Estratto}%
\def\ackname{Ringraziamenti}%
\def\andname{E}%
\def\bibname{Bibliografia}%
\def\lastandname{, E}%
\def\appendixname{Appendice}%
\def\chaptername{Capitolo}%
\def\claimname{Reclamo}%
\def\conjecturename{Congettura}%
\def\contentsname{Contenuti}%
\def\corollaryname{Corollario}%
\def\definitionname{Definizione}%
\def\emailname{e-mail}%
\def\examplename{Esempio}%
\def\exercisename{Esercizio}%
\def\figurename{Figura}%
\def\forewordname{Prefazione}%
\def\keywordname{{\bf Parole chiave:}}%
\def\indexname{Indice}%
\def\lemmaname{Lemma}%
\def\contriblistname{Elenco dei collaboratori}%
\def\listfigurename{Elenco delle figure}%
\def\listtablename{Elenco delle tabelle}%
\def\mailname{{\it Corrispondenza a\/}:}%
\def\noteaddname{Nota aggiunta nelle bozze}%
\def\notename{Nota}%
\def\partname{Parte}%
\def\prefacename{Prefazione}%
\def\problemname{Problema}%
\def\proofname{Prova}%
\def\propertyname{Propriet\`{a}}%
\def\propositionname{Proposizione}%
\def\questionname{Domanda}%
\def\refname{Riferimenti bibliografici}%
\def\remarkname{Commento}%
\def\seename{Vedi}%
\def\solutionname{Soluzione}%
\def\subclassname{{\it Classificazioni degli argomenti\/}:}%
\def\tablename{Tabella}%
\def\theoremname{Teorema}%

</rewritten_file> 