This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=pdflatex 2025.3.22)  30 MAY 2025 14:14
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex
(/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(./svmult.cls) (/usr/local/texlive/2025/texmf-dist/tex/latex/type1cm/type1cm.sty
Package: type1cm 2002/09/05 v0.04 BlueSky/Y&Y Type1 CM font definitions (DPC, patched RF)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/makeidx.sty
Package: makeidx 2021/10/04 v1.0m Standard LaTeX package
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen141
\Gin@req@width=\dimen142
) (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/multicol.sty
Package: multicol 2024/09/14 v1.9i multicolumn formatting (FMi)
\c@tracingmulticols=\count196
\mult@box=\box52
\multicol@leftmargin=\dimen143
\c@unbalance=\count197
\c@collectmore=\count198
\doublecol@number=\count199
\multicoltolerance=\count266
\multicolpretolerance=\count267
\full@width=\dimen144
\page@free=\dimen145
\premulticols=\dimen146
\postmulticols=\dimen147
\multicolsep=\skip49
\multicolbaselineskip=\skip50
\partial@page=\box53
\last@line=\box54
\mc@boxedresult=\box55
\maxbalancingoverflow=\dimen148
\mult@rightbox=\box56
\mult@grightbox=\box57
\mult@firstbox=\box58
\mult@gfirstbox=\box59
\@tempa=\box60
\@tempa=\box61
\@tempa=\box62
\@tempa=\box63
\@tempa=\box64
\@tempa=\box65
\@tempa=\box66
\@tempa=\box67
\@tempa=\box68
\@tempa=\box69
\@tempa=\box70
\@tempa=\box71
\@tempa=\box72
\@tempa=\box73
\@tempa=\box74
\@tempa=\box75
\@tempa=\box76
\@tempa=\box77
\@tempa=\box78
\@tempa=\box79
\@tempa=\box80
\@tempa=\box81
\@tempa=\box82
\@tempa=\box83
\@tempa=\box84
\@tempa=\box85
\@tempa=\box86
\@tempa=\box87
\@tempa=\box88
\@tempa=\box89
\@tempa=\box90
\@tempa=\box91
\@tempa=\box92
\@tempa=\box93
\@tempa=\box94
\@tempa=\box95
\c@minrows=\count268
\c@columnbadness=\count269
\c@finalcolumnbadness=\count270
\last@try=\dimen149
\multicolovershoot=\dimen150
\multicolundershoot=\dimen151
\mult@nat@firstbox=\box96
\colbreak@box=\box97
\mc@col@check@num=\count271
) (/usr/local/texlive/2025/texmf-dist/tex/latex/footmisc/footmisc.sty
Package: footmisc 2024/12/24 v6.0g a miscellany of footnote facilities
\FN@temptoken=\toks18
\footnotemargin=\dimen152
\@outputbox@depth=\dimen153
Package footmisc Info: Declaring symbol style bringhurst on input line 699.
Package footmisc Info: Declaring symbol style chicago on input line 707.
Package footmisc Info: Declaring symbol style wiley on input line 716.
Package footmisc Info: Declaring symbol style lamport-robust on input line 727.
Package footmisc Info: Declaring symbol style lamport* on input line 747.
Package footmisc Info: Declaring symbol style lamport*-robust on input line 768.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/newtxtext.sty
Package: newtxtext 2024/04/01 v1.744(Michael Sharpe) latex and unicode latex support for TeXGyreTermesX
 `newtxtext' v1.744, 2024/04/01 Text macros taking advantage of TeXGyre Termes and its extensions (msharpe) (/usr/local/texlive/2025/texmf-dist/tex/latex/xpatch/xpatch.sty (/usr/local/texlive/2025/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-01-18 L3 programming layer (loader) 
 (/usr/local/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count272
\l__pdf_internal_box=\box98
))
Package: xpatch 2020/03/25 v0.3a Extending etoolbox patching commands
 (/usr/local/texlive/2025/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-08-16 L3 Experimental document command parser
) (/usr/local/texlive/2025/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count273
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
) (/usr/local/texlive/2025/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/xkeyval/xkeyval.tex (/usr/local/texlive/2025/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks19
\XKV@tempa@toks=\toks20
)
\XKV@depth=\count274
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
) (/usr/local/texlive/2025/texmf-dist/tex/generic/xstring/xstring.sty (/usr/local/texlive/2025/texmf-dist/tex/generic/xstring/xstring.tex
\xs_counta=\count275
\xs_countb=\count276
)
Package: xstring 2023/08/22 v1.86 String manipulations (CT)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/carlisle/scalefnt.sty)
LaTeX Font Info:    Setting ntxLF sub-encoding to TS1/0 on input line 24.
LaTeX Font Info:    Setting ntxTLF sub-encoding to TS1/0 on input line 24.
LaTeX Font Info:    Setting ntxOsF sub-encoding to TS1/0 on input line 24.
LaTeX Font Info:    Setting ntxTOsF sub-encoding to TS1/0 on input line 24.
 (/usr/local/texlive/2025/texmf-dist/tex/generic/kastrup/binhex.tex)
\ntx@tmpcnta=\count277
\ntx@cnt=\count278
 (/usr/local/texlive/2025/texmf-dist/tex/latex/fontaxes/fontaxes.sty
Package: fontaxes 2020/07/21 v1.0e Font selection axes
LaTeX Info: Redefining \upshape on input line 29.
LaTeX Info: Redefining \itshape on input line 31.
LaTeX Info: Redefining \slshape on input line 33.
LaTeX Info: Redefining \swshape on input line 35.
LaTeX Info: Redefining \scshape on input line 37.
LaTeX Info: Redefining \sscshape on input line 39.
LaTeX Info: Redefining \ulcshape on input line 41.
LaTeX Info: Redefining \textsw on input line 47.
LaTeX Info: Redefining \textssc on input line 48.
LaTeX Info: Redefining \textulc on input line 49.
)
\tx@sixem=\dimen154
\tx@y=\dimen155
\tx@x=\dimen156
\tx@tmpdima=\dimen157
\tx@tmpdimb=\dimen158
\tx@tmpdimc=\dimen159
\tx@tmpdimd=\dimen160
\tx@tmpdime=\dimen161
\tx@tmpdimf=\dimen162
\tx@dimA=\dimen163
\tx@dimAA=\dimen164
\tx@dimB=\dimen165
\tx@dimBB=\dimen166
\tx@dimC=\dimen167
LaTeX Info: Redefining \oldstylenums on input line 902.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/newtxmath.sty
Package: newtxmath 2024/09/22 v1.754
 `newtxmath' v1.754, 2024/09/22 Math macros based originally on txfonts (msharpe) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks21
\ex@=\dimen168
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen169
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count279
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count280
\leftroot@=\count281
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count282
\DOTSCASE@=\count283
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box99
\strutbox@=\box100
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen170
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count284
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count285
\dotsspace@=\muskip17
\c@parentequation=\count286
\dspbrk@lvl=\count287
\tag@help=\toks22
\row@=\count288
\column@=\count289
\maxfields@=\count290
\andhelp@=\toks23
\eqnshift@=\dimen171
\alignsep@=\dimen172
\tagshift@=\dimen173
\tagwidth@=\dimen174
\totwidth@=\dimen175
\lineht@=\dimen176
\@envbody=\toks24
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks25
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/oberdiek/centernot.sty
Package: centernot 2016/05/16 v1.4 Centers the not symbol horizontally (HO)
)
\tx@cntz=\count291
 (/usr/local/texlive/2025/texmf-dist/tex/generic/kastrup/binhex.tex)
\tx@Isdigit=\count292
\tx@IsAlNum=\count293
\tx@tA=\toks26
\tx@tB=\toks27
\tx@su=\read2

amsthm NOT loaded
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 402.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/minntx/m/n on input line 402.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/minntx/m/n on input line 402.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/minntx/m/n --> OT1/minntx/b/n on input line 403.
LaTeX Font Info:    Redeclaring math alphabet \mathsf on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> OT1/qhv/m/n on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> OT1/qhv/m/n on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/qhv/m/n --> OT1/qhv/b/n on input line 412.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 419.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/minntx/m/it on input line 419.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/minntx/m/it on input line 419.
LaTeX Font Info:    Redeclaring math alphabet \mathtt on input line 420.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> OT1/ntxtt/m/n on input line 420.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> OT1/ntxtt/m/n on input line 420.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 422.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/minntx/b/n on input line 422.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/minntx/b/n on input line 422.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/minntx/m/it --> OT1/minntx/b/it on input line 423.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/ntxtt/m/n --> OT1/ntxtt/b/n on input line 426.
LaTeX Font Info:    Redeclaring symbol font `letters' on input line 534.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/ntxmi/m/it on input line 534.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/ntxmi/m/it on input line 534.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/ntxmi/m/it --> OML/ntxmi/b/it on input line 535.
\symlettersA=\mathgroup4
LaTeX Font Info:    Overwriting symbol font `lettersA' in version `bold'
(Font)                  U/ntxmia/m/it --> U/ntxmia/b/it on input line 582.
Now handling font encoding LMS ...
... no UTF-8 mapping file for font encoding LMS
LaTeX Font Info:    Redeclaring symbol font `symbols' on input line 604.
LaTeX Font Info:    Encoding `OMS' has changed to `LMS' for symbol font
(Font)              `symbols' in the math version `normal' on input line 604.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> LMS/ntxsy/m/n on input line 604.
LaTeX Font Info:    Encoding `OMS' has changed to `LMS' for symbol font
(Font)              `symbols' in the math version `bold' on input line 604.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> LMS/ntxsy/m/n on input line 604.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  LMS/ntxsy/m/n --> LMS/ntxsy/b/n on input line 605.
\symAMSm=\mathgroup5
LaTeX Font Info:    Overwriting symbol font `AMSm' in version `bold'
(Font)                  U/ntxsym/m/n --> U/ntxsym/b/n on input line 630.
\symsymbolsC=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `symbolsC' in version `bold'
(Font)                  U/ntxsyc/m/n --> U/ntxsyc/b/n on input line 651.
Now handling font encoding LMX ...
... no UTF-8 mapping file for font encoding LMX
LaTeX Font Info:    Redeclaring symbol font `largesymbols' on input line 664.
LaTeX Font Info:    Encoding `OMX' has changed to `LMX' for symbol font
(Font)              `largesymbols' in the math version `normal' on input line 664.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> LMX/ntxexx/m/n on input line 664.
LaTeX Font Info:    Encoding `OMX' has changed to `LMX' for symbol font
(Font)              `largesymbols' in the math version `bold' on input line 664.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> LMX/ntxexx/m/n on input line 664.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  LMX/ntxexx/m/n --> LMX/ntxexx/b/n on input line 665.
\symlargesymbolsTXA=\mathgroup7
LaTeX Font Info:    Overwriting symbol font `largesymbolsTXA' in version `bold'
(Font)                  U/ntxexa/m/n --> U/ntxexa/b/n on input line 679.
\tx@sbptoks=\toks28
LaTeX Font Info:    Redeclaring math delimiter \lfloor on input line 902.
LaTeX Font Info:    Redeclaring math delimiter \rfloor on input line 903.
LaTeX Font Info:    Redeclaring math delimiter \lceil on input line 904.
LaTeX Font Info:    Redeclaring math delimiter \rceil on input line 905.
LaTeX Font Info:    Redeclaring math delimiter \lbrace on input line 910.
LaTeX Font Info:    Redeclaring math delimiter \rbrace on input line 911.
LaTeX Font Info:    Redeclaring math delimiter \langle on input line 913.
LaTeX Font Info:    Redeclaring math delimiter \rangle on input line 915.
LaTeX Font Info:    Redeclaring math delimiter \arrowvert on input line 919.
LaTeX Font Info:    Redeclaring math delimiter \vert on input line 920.
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 966.
LaTeX Font Info:    Redeclaring math accent \dot on input line 991.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 992.
LaTeX Font Info:    Redeclaring math accent \vec on input line 2057.
LaTeX Info: Redefining \Bbbk on input line 2847.
LaTeX Info: Redefining \not on input line 2995.
)
\@indexfile=\write3
\openout3 = `paper1-skin-disease.idx'.


Writing index file paper1-skin-disease.idx
LaTeX Font Info:    Trying to load font information for OT1+ntxtlf on input line 18.
(/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/ot1ntxtlf.fd
File: ot1ntxtlf.fd 2021/05/24 v1.0 font definition file for OT1/ntx/tlf
)
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 10.0pt on input line 18.
 (./paper1-skin-disease.aux)
\openout1 = `paper1-skin-disease.aux'.

LaTeX Font Info:    Checking defaults for OML/ntxmi/m/it on input line 18.
LaTeX Font Info:    Trying to load font information for OML+ntxmi on input line 18.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/omlntxmi.fd
File: omlntxmi.fd 2015/08/25 Fontinst v1.933 font definitions for OML/ntxmi.
)
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 18.
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 18.
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 18.
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 18.
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 18.
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for U/ntxexa/m/n on input line 18.
LaTeX Font Info:    Trying to load font information for U+ntxexa on input line 18.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/untxexa.fd
File: untxexa.fd 2012/04/16 Fontinst v1.933 font definitions for U/ntxexa.
)
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for LMS/ntxsy/m/n on input line 18.
LaTeX Font Info:    Trying to load font information for LMS+ntxsy on input line 18.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/lmsntxsy.fd
File: lmsntxsy.fd 2016/07/02 Fontinst v1.933 font definitions for LMS/ntxsy.
)
LaTeX Font Info:    ... okay on input line 18.
LaTeX Font Info:    Checking defaults for LMX/ntxexx/m/n on input line 18.
LaTeX Font Info:    Trying to load font information for LMX+ntxexx on input line 18.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/lmxntxexx.fd
File: lmxntxexx.fd 2016/07/03 Fontinst v1.933 font definitions for LMX/ntxexx.
)
LaTeX Font Info:    ... okay on input line 18.


/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:18: LaTeX Error: The font size command \normalsize is not defined:
               there is probably something wrong with the class file.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.18 \begin{document}
                     
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

(/usr/local/texlive/2025/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count294
\scratchdimen=\dimen177
\scratchbox=\box101
\nofMPsegments=\count295
\nofMParguments=\count296
\everyMPshowfont=\toks29
\MPscratchCnt=\count297
\MPscratchDim=\dimen178
\MPnumerator=\count298
\makeMPintoPDFobject=\count299
\everyMPtoPDFconversion=\toks30
) (/usr/local/texlive/2025/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
 (/usr/local/texlive/2025/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/grfext/grfext.sty
Package: grfext 2019/12/03 v1.3 Manage graphics extensions (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)) (/usr/local/texlive/2025/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
Package grfext Info: Graphics extension search list:
(grfext)             [.pdf,.png,.jpg,.mps,.jpeg,.jbig2,.jb2,.PDF,.PNG,.JPG,.JPEG,.JBIG2,.JB2,.eps]
(grfext)             \AppendGraphicsExtensions on input line 504.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
\c@mv@tabular=\count300
\c@mv@boldtabular=\count301
LaTeX Info: Command `\dddot' is already robust on input line 18.
LaTeX Info: Command `\ddddot' is already robust on input line 18.

/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:24: Undefined control sequence.
l.24 \institute
               {Author Name \at Affiliation, Address, \email{email@example.c...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:24: Undefined control sequence.
l.24 \institute{Author Name \at
                                Affiliation, Address, \email{email@example.c...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:24: Undefined control sequence.
l.24 ...thor Name \at Affiliation, Address, \email
                                                  {<EMAIL>}}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:26: Undefined control sequence.
l.26 \maketitle
               
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:28: Undefined control sequence.
l.28 \abstract
              *{Abstract goes here. (max 200 words)}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:30: Undefined control sequence.
l.30 \abstract
              {Abstract goes here. (max 200 words)}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:32: Undefined control sequence.
l.32 \section
             {Introduction}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:36: Undefined control sequence.
l.36 \section
             {Methods}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:40: Undefined control sequence.
l.40 \section
             {Results}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:44: Undefined control sequence.
l.44 \section
             {Discussion}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:49: LaTeX Error: Environment figure undefined.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.49 \begin{figure}
                   [b]
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:50: Undefined control sequence.
l.50 \sidecaption
                 
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

<figures/example-figure.png, id=1, 172.645pt x 178.6675pt>
File: figures/example-figure.png Graphic file (type png)
<use figures/example-figure.png>
Package pdftex.def Info: figures/example-figure.png  used on input line 51.
(pdftex.def)             Requested size: 4915.24998pt x 5090.97504pt.

/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:52: LaTeX Error: \caption outside float.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.52 \caption
             {Example figure caption.}
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:54: LaTeX Error: \begin{document} ended by \end{figure}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.54 \end{figure}
                 
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:57: LaTeX Error: Environment table undefined.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.57 \begin{table}
                  [!t]
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:58: LaTeX Error: \caption outside float.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.58 \caption
             {Example table caption}
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

LaTeX Font Info:    Trying to load font information for OT1+minntx on input line 60.
(/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/ot1minntx.fd
File: ot1minntx.fd 2023/09/09 v1.1 font definition file for OT1/minntx
)
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 10.0pt on input line 60.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 7.3pt on input line 60.
LaTeX Font Info:    Font shape `OT1/minntx/m/n' will be
(Font)              scaled to size 5.5pt on input line 60.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 10.0pt on input line 60.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 7.3pt on input line 60.
LaTeX Font Info:    Font shape `OML/ntxmi/m/it' will be
(Font)              scaled to size 5.5pt on input line 60.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 10.0pt on input line 60.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 7.3pt on input line 60.
LaTeX Font Info:    Font shape `LMS/ntxsy/m/n' will be
(Font)              scaled to size 5.5pt on input line 60.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 10.0pt on input line 60.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 7.3pt on input line 60.
LaTeX Font Info:    Font shape `LMX/ntxexx/m/n' will be
(Font)              scaled to size 5.5pt on input line 60.
LaTeX Font Info:    Trying to load font information for U+ntxmia on input line 60.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/untxmia.fd
File: untxmia.fd 2024/04/09 Fontinst v1.933 font definitions for U/ntxmia.
)
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 10.0pt on input line 60.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 7.3pt on input line 60.
LaTeX Font Info:    Font shape `U/ntxmia/m/it' will be
(Font)              scaled to size 5.5pt on input line 60.
LaTeX Font Info:    Trying to load font information for U+ntxsym on input line 60.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/untxsym.fd
File: untxsym.fd 2023/08/16 Fontinst v1.933 font definitions for U/ntxsym.
)
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 10.0pt on input line 60.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 7.3pt on input line 60.
LaTeX Font Info:    Font shape `U/ntxsym/m/n' will be
(Font)              scaled to size 5.5pt on input line 60.
LaTeX Font Info:    Trying to load font information for U+ntxsyc on input line 60.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/newtx/untxsyc.fd
File: untxsyc.fd 2012/04/12 Fontinst v1.933 font definitions for U/ntxsyc.
)
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 10.0pt on input line 60.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 7.3pt on input line 60.
LaTeX Font Info:    Font shape `U/ntxsyc/m/n' will be
(Font)              scaled to size 5.5pt on input line 60.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 10.0pt on input line 60.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 7.3pt on input line 60.
LaTeX Font Info:    Font shape `U/ntxexa/m/n' will be
(Font)              scaled to size 5.5pt on input line 60.

/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:63: Undefined control sequence.
l.63 \noalign{\smallskip}\svhline
                                 \noalign{\smallskip}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:67: LaTeX Error: \begin{document} ended by \end{table}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.67 \end{table}
                
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:70: LaTeX Error: Environment theorem undefined.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.70 \begin{theorem}
                    
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:72: LaTeX Error: \begin{document} ended by \end{theorem}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.72 \end{theorem}
                  
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:75: LaTeX Error: Environment definition undefined.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.75 \begin{definition}
                       
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:77: LaTeX Error: \begin{document} ended by \end{definition}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.77 \end{definition}
                     
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:80: LaTeX Error: Environment proof undefined.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.80 \begin{proof}
                  
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:82: LaTeX Error: \begin{document} ended by \end{proof}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.82 \end{proof}
                
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:85: LaTeX Error: Environment acknowledgement undefined.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.85 \begin{acknowledgement}
                            
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:87: LaTeX Error: \begin{document} ended by \end{acknowledgement}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.87 \end{acknowledgement}
                          
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:90: Undefined control sequence.
l.90 \ethics
            {Competing Interests}{Statement here.}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:91: Undefined control sequence.
l.91 \ethics
            {Ethics Approval}{Statement here.}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:94: Undefined control sequence.
l.94 \section
             *{Appendix}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

(./references.tex

./references.tex:5: LaTeX Error: Environment thebibliography undefined.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.5 \begin{thebibliography}
                           {99.}
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


./references.tex:7: LaTeX Error: Lonely \item--perhaps a missing list environment.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.7 \bibitem{sample-article}
                             Author, A., Author, B.: Title of the article. J...
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

./references.tex:7: Undefined control sequence.
<argument> \@listctr 
                     
l.7 \bibitem{sample-article}
                             Author, A., Author, B.: Title of the article. J...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./references.tex:7: You can't use `\relax' after \the.
<recently read> \c@ 
                    
l.7 \bibitem{sample-article}
                             Author, A., Author, B.: Title of the article. J...
I'm forgetting what you said and using zero instead.

LaTeX Font Info:    Font shape `OT1/ntxtlf/b/n' will be
(Font)              scaled to size 10.0pt on input line 7.

./references.tex:9: LaTeX Error: Lonely \item--perhaps a missing list environment.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.9 \bibitem{sample-book}
                          Author, C.: Title of the Book. Publisher, City (2023)
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

./references.tex:9: Undefined control sequence.
<argument> \@listctr 
                     
l.9 \bibitem{sample-book}
                          Author, C.: Title of the Book. Publisher, City (2023)
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./references.tex:9: You can't use `\relax' after \the.
<recently read> \c@ 
                    
l.9 \bibitem{sample-book}
                          Author, C.: Title of the Book. Publisher, City (2023)
I'm forgetting what you said and using zero instead.


./references.tex:11: LaTeX Error: Lonely \item--perhaps a missing list environment.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.11 \bibitem{sample-chapter}
                              Author, D.: Title of the Chapter. In: Editor, ...
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

./references.tex:11: Undefined control sequence.
<argument> \@listctr 
                     
l.11 \bibitem{sample-chapter}
                              Author, D.: Title of the Chapter. In: Editor, ...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./references.tex:11: You can't use `\relax' after \the.
<recently read> \c@ 
                    
l.11 \bibitem{sample-chapter}
                              Author, D.: Title of the Chapter. In: Editor, ...
I'm forgetting what you said and using zero instead.


./references.tex:13: LaTeX Error: \begin{document} ended by \end{thebibliography}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.13 \end{thebibliography}
                          
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

)


/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:101: LaTeX Error: The font size command \normalsize is not defined:
               there is probably something wrong with the class file.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.101 \end{document}
                    
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.

[1{/usr/local/texlive/2025/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{/usr/local/texlive/2025/texmf-dist/fonts/enc/dvips/newtx/ntx-ot1-tlf.enc}
/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:101: Undefined control sequence.
<write> \newlabel{sec:intro}{{}{\thepage 
                                         }{}{}{}}
l.101 \end{document}
                    
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:101: Undefined control sequence.
<write> \newlabel{sec:methods}{{}{\thepage 
                                           }{}{}{}}
l.101 \end{document}
                    
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:101: Undefined control sequence.
<write> \newlabel{sec:results}{{}{\thepage 
                                           }{}{}{}}
l.101 \end{document}
                    
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:101: Undefined control sequence.
<write> \newlabel{sec:discussion}{{}{\thepage 
                                              }{}{}{}}
l.101 \end{document}
                    
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:101: Undefined control sequence.
<write> \newlabel{fig:example}{{}{\thepage 
                                           }{}{}{}}
l.101 \end{document}
                    
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:101: Undefined control sequence.
<write> \newlabel{tab:example}{{}{\thepage 
                                           }{}{}{}}
l.101 \end{document}
                    
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

/Users/<USER>/Documents/GitHub/cai-fm4healthcare-workshop/paper1-skin-disease/paper1-skin-disease.tex:101: Undefined control sequence.
<write> ...tentsline {section}{Appendix}{\thepage 
                                                  }{}\protected@file@percent }
l.101 \end{document}
                    
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

 <./figures/example-figure.png>] (./paper1-skin-disease.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2020/03/25>
 ***********


LaTeX Warning: Label(s) may have changed. Rerun to get cross-references right.

 ) 
Here is how much of TeX's memory you used:
 7455 strings out of 473190
 110019 string characters out of 5715800
 485126 words of memory out of 5000000
 30576 multiletter control sequences out of 15000+600000
 573535 words of font info for 65 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 80i,8n,93p,400b,190s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/local/texlive/2025/texmf-dist/fonts/type1/public/newtx/ztmb.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/newtx/ztmr.pfb>
Output written on paper1-skin-disease.pdf (1 page, 67840 bytes).
PDF statistics:
 21 PDF objects out of 1000 (max. 8388607)
 11 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 6 words of extra memory for PDF output out of 10000 (max. 10000000)

