%%%%%%%%%%%%%%%%%%%% paper1-skin-disease.tex %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Template for a Springer SVMult article (see authorsample.tex)
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\documentclass[graybox]{svmult}

% Standard packages
\usepackage{type1cm}
\usepackage{makeidx}
\usepackage{graphicx}
\usepackage{multicol}
\usepackage[bottom]{footmisc}
\usepackage{newtxtext}
\usepackage[varvw]{newtxmath}

\makeindex

\begin{document}

\title*{Title of the Article}
% \titlerunning{Short Title} % optional
\author{Author Name\orcidID{0000-0000-0000-0000}}
% \authorrunning{Short Author} % optional
\institute{Author Name \at Affiliation, Address, \email{<EMAIL>}}

\maketitle

\abstract*{Abstract goes here. (max 200 words)}

\abstract{Abstract goes here. (max 200 words)}

\section{Introduction}
\label{sec:intro}
% Your introduction text here.

\section{Methods}
\label{sec:methods}
% Your methods text here.

\section{Results}
\label{sec:results}
% Your results text here.

\section{Discussion}
\label{sec:discussion}
% Your discussion text here.

% Example figure (place your figures in a 'figures/' subfolder)
\begin{figure}[b]
\sidecaption
\includegraphics[width=0.6\textwidth]{figures/example-figure.png}
\caption{Example figure caption.}
\label{fig:example}
\end{figure}

% Example table
\begin{table}[!t]
\caption{Example table caption}
\label{tab:example}
\begin{tabular}{p{2cm}p{2.4cm}p{2cm}p{4.9cm}}
\hline\noalign{\smallskip}
Col1 & Col2 & Col3 & Col4 \\
\noalign{\smallskip}\svhline\noalign{\smallskip}
A & B & C & D \\
\noalign{\smallskip}\hline\noalign{\smallskip}
\end{tabular}
\end{table}

% Example theorem
\begin{theorem}
Theorem text goes here.
\end{theorem}

% Example definition
\begin{definition}
Definition text goes here.
\end{definition}

% Example proof
\begin{proof}
Proof text goes here.
\end{proof}

% Acknowledgements (optional)
\begin{acknowledgement}
Acknowledgements go here.
\end{acknowledgement}

% Ethics statements (optional)
\ethics{Competing Interests}{Statement here.}
\ethics{Ethics Approval}{Statement here.}

% Appendix (optional)
\section*{Appendix}
\addcontentsline{toc}{section}{Appendix}
% Appendix content here.

% References
\input{references}

\end{document} 